﻿import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TextFieldModule } from '@angular/cdk/text-field'; // For cdkTextareaAutosize

import { ScheduleReportConfig } from '../../models/schedule-report.model';
import { ReportParameters } from '../../models/report-parameters.model';


@Component({
  selector: 'app-schedule-report-dialog',
  standalone: true,
  imports: [
    CommonModule, ReactiveFormsModule, MatDialogModule, MatFormFieldModule,
    MatInputModule, MatSelectModule, MatDatepickerModule, MatCheckboxModule,
    MatRadioModule, MatButtonModule, MatIconModule, TextFieldModule
  ],
  templateUrl: './schedule-report-dialog.component.html',
  styleUrls: ['./schedule-report-dialog.component.scss'],
})
export class ScheduleReportDialogComponent implements OnInit {
  scheduleForm!: FormGroup;
  daysInMonth: number[] = Array.from({ length: 31 }, (_, i) => i + 1);

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<ScheduleReportDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { currentParameters?: ReportParameters }
  ) {}

  ngOnInit(): void {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    this.scheduleForm = this.fb.group({
      reportName: ['Employee Time Card Report - Monthly', Validators.required],
      frequency: ['monthly', Validators.required],
      dayOfMonth: [1], // Default for monthly
      startDate: [tomorrow.toISOString().split('T')[0], Validators.required], // Default to tomorrow
      deliveryEmail: [true],
      deliverySaveToDirectory: [false],
      emailRecipients: ['<EMAIL>, <EMAIL>'], // Initial value, validator applied below
      reportFormat: ['pdf', Validators.required],
      includeCurrentParameters: [true],
    });

    // Add conditional validator for emailRecipients
    this.scheduleForm.get('deliveryEmail')?.valueChanges.subscribe(checked => {
        const emailRecipientsControl = this.scheduleForm.get('emailRecipients');
        if (checked) {
            emailRecipientsControl?.setValidators([Validators.required, this.emailListValidator]);
        } else {
            emailRecipientsControl?.clearValidators();
        }
        emailRecipientsControl?.updateValueAndValidity();
    });
    // Trigger initial validation state for emailRecipients
    this.scheduleForm.get('emailRecipients')?.updateValueAndValidity();
  }

  emailListValidator(control: AbstractControl): { [key: string]: boolean } | null {
    if (control.value) {
      const emails = control.value.split(',').map((e: string) => e.trim());
      const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      const invalidEmails = emails.filter((e: string) => e && !emailPattern.test(e));
      if (invalidEmails.length > 0) {
        return { invalidEmailFormat: true };
      }
    }
    return null;
  }


  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    if (this.scheduleForm.valid) {
      const formValue = this.scheduleForm.value;
      const config: ScheduleReportConfig = {
        ...formValue,
        startDate: new Date(formValue.startDate).toISOString(), // Ensure ISO format
        emailRecipients: formValue.deliveryEmail ? formValue.emailRecipients.split(',').map((s: string) => s.trim()).filter((s:string) => s) : []
      };
      this.dialogRef.close(config);
    }
  }
}