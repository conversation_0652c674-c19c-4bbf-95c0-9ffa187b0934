﻿export interface EmployeeTimeCard {
  id: string;
  firstName: string;
  lastName: string;
  employeeType: string;
  department: string;
  supervisor: string;
  teamLead?: string;
  utilization: number; // Percentage e.g. 87.5
  billable: number; // hours
  nonBillable: number; // hours
  totalHours: number; // hours
  amount: number; // currency
  comments?: EmployeeComment[];
}

export interface EmployeeComment {
  id: number;
  date: string; // ISO date string
  text: string;
  author: string;
}