import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

interface Department {
  name: string;
  description: string;
  employees: number;
  color: string;
}

interface EmploymentType {
  name: string;
  description: string;
  employees: number;
}

interface JobTitle {
  name: string;
  department: string;
  description: string;
  employees: number;
}

@Component({
  selector: 'app-company-settings',
  templateUrl: './company-settings.component.html',
  styleUrls: ['./company-settings.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class CompanySettingsComponent {
  companyForm: FormGroup;
  departments: Department[] = [
    { name: 'Technology', description: 'Software development and technical projects', employees: 45, color: '#C3E5AE' },
    { name: 'Product', description: 'Product strategy and development lifecycle', employees: 25, color: '#A7C5EB' },
    { name: 'Design', description: 'Product and graphic design', employees: 18, color: '#FFD6E0' },
    { name: 'Marketing', description: 'Brand communications and creative acquisition', employees: 22, color: '#A7C5EB' },
    { name: 'Analytics', description: 'Data analysis and reporting', employees: 15, color: '#BEE3F8' },
    { name: 'Human Resources', description: 'Employee relations and recruitment', employees: 10, color: '#D6CDEA' },
    { name: 'Finance', description: 'Accounting, budgeting, and financial planning', employees: 12, color: '#FFEE93' },
    { name: 'IT', description: 'Internal IT support and infrastructure', employees: 8, color: '#C6D8AF' }
  ];

  employmentTypes: EmploymentType[] = [
    { name: 'Full-time', description: 'Standard 40-hour work week with benefits', employees: 85 },
    { name: 'Part-time', description: 'Less than 40-hour work week', employees: 12 },
    { name: 'Contractor', description: 'External workers on contract basis', employees: 28 },
    { name: 'Intern', description: 'Temporary position for students or trainees', employees: 5 }
  ];

  jobTitles: JobTitle[] = [
    { name: 'Software Engineer', department: 'Technology', description: 'Develops and maintains software applications', employees: 25 },
    { name: 'Product Manager', department: 'Product', description: 'Leads product strategy and roadmap', employees: 8 },
    { name: 'UX Designer', department: 'Design', description: 'Creates user experience designs', employees: 6 },
    { name: 'Marketing Specialist', department: 'Marketing', description: 'Executes marketing campaigns', employees: 10 },
    { name: 'Data Analyst', department: 'Analytics', description: 'Analyzes business data and metrics', employees: 7 },
    { name: 'HR Manager', department: 'Human Resources', description: 'Manages HR operations and policies', employees: 3 },
    { name: 'Financial Analyst', department: 'Finance', description: 'Performs financial analysis and reporting', employees: 5 },
    { name: 'IT Support', department: 'IT', description: 'Provides technical support and maintenance', employees: 4 }
  ];

  constructor(private fb: FormBuilder) {
    this.companyForm = this.fb.group({
      companyName: ['Pathfinder Analytics Inc.', [Validators.required]],
      taxId: ['98-7654321', [Validators.required]],
      phone: ['(*************', [Validators.required]],
      email: ['<EMAIL>', [Validators.required, Validators.email]],
      address: ['']
    });
  }

  saveChanges(): void {
    if (this.companyForm.valid) {
      console.log('Saving company information:', this.companyForm.value);
      // TODO: Implement save functionality
    }
  }

  addDepartment(): void {
    // TODO: Implement add department dialog
  }

  editDepartment(department: Department): void {
    // TODO: Implement edit department dialog
  }

  deleteDepartment(department: Department): void {
    // TODO: Implement delete department
  }

  addEmploymentType(): void {
    // TODO: Implement add employment type dialog
  }

  editEmploymentType(type: EmploymentType): void {
    // TODO: Implement edit employment type dialog
  }

  deleteEmploymentType(type: EmploymentType): void {
    // TODO: Implement delete employment type
  }

  addJobTitle(): void {
    // TODO: Implement add job title dialog
  }

  editJobTitle(title: JobTitle): void {
    // TODO: Implement edit job title dialog
  }

  deleteJobTitle(title: JobTitle): void {
    // TODO: Implement delete job title
  }
}