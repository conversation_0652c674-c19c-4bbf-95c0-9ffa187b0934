﻿.app-header-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  position: sticky;
  top: 0;
  z-index: 1000; // Ensure it stays on top
  background-color: white; // As per mock
  border-bottom: 1px solid #e0e0e0; // As per mock (border-gray-200)
  color: #333; // Default text color for items not themed by primary

  .toolbar-left,
  .toolbar-right {
    display: flex;
    align-items: center;
  }

  .breadcrumbs {
    margin-left: 16px;
    font-size: 0.875rem; // text-sm

    .breadcrumb-link {
      color: #2563eb; // text-blue-600
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
    .breadcrumb-separator {
      color: #9ca3af; // text-gray-500
      margin: 0 4px;
    }
    .breadcrumb-current {
      color: #4b5563; // text-gray-700
    }
  }

  .user-initials {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #e0e0e0; // Placeholder background
    color: #333;
    font-weight: 500;
    margin-right: 8px;
  }

  button[mat-icon-button] {
    color: #4b5563; // text-gray-600
    &:hover {
      background-color: #f3f4f6; // hover:bg-gray-100
    }
  }
}

// Override primary color for toolbar if needed, or use a custom theme
// For now, using 'primary' and styling elements within.
// If the toolbar itself should be white:
// .app-header-toolbar.mat-toolbar-single-row {
//   background: white;
//   color: #333; // Adjust text color accordingly
// }