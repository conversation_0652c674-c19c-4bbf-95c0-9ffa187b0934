﻿import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
// import { NgxChartsModule } from '@swimlane/ngx-charts'; // Import if using ngx-charts

@Component({
  selector: 'app-chart-wrapper',
  standalone: true,
  imports: [CommonModule /*, NgxChartsModule */], // Add NgxChartsModule if used
  templateUrl: './chart-wrapper.component.html',
  styleUrls: ['./chart-wrapper.component.scss'],
})
export class ChartWrapperComponent {
  @Input() chartTitle?: string;
  @Input() chartType: 'bar' | 'line' | 'pie' = 'bar';
  @Input() data: any[] = []; // Data for the chart
  @Input() options?: any; // Chart specific options

  // Placeholder for ngx-charts specific inputs (example)
  @Input() view: [number, number] | undefined = undefined; // [width, height]
  @Input() colorScheme: any = { domain: ['#5AA454', '#A10A28', '#C7B42C', '#AAAAAA'] };
  @Input() results: any[] = [];
  @Input() gradient: boolean = false;
  @Input() showXAxis: boolean = true;
  @Input() showYAxis: boolean = true;
  @Input() showLegend: boolean = false;
  @Input() showXAxisLabel: boolean = true;
  @Input() showYAxisLabel: boolean = true;
  @Input() xAxisLabel: string = '';
  @Input() yAxisLabel: string = '';

  // For mock axis labels
  @Input() showAxisLabels: boolean = false;
  @Input() xAxisData?: string[];
  @Input() yAxisData?: string[];


  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onSelect(event: any): void {
    console.log('Chart item selected', event);
  }
}