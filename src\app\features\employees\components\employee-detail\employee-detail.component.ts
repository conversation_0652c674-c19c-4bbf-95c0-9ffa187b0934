import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { EmployeeService } from '../../data-access/employee.service';
import { Employee } from '../../../../models/employee.model';
import { LoadingSpinnerComponent } from '../../../../shared/ui/loading-spinner/loading-spinner.component';
import { AddEmployeeComponent } from '../add-employee/add-employee.component';

@Component({
  selector: 'app-employee-detail',
  templateUrl: './employee-detail.component.html',
  styleUrls: ['./employee-detail.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule, LoadingSpinnerComponent, AddEmployeeComponent]
})
export class EmployeeDetailComponent implements OnInit {
  employeeId: string = '';
  employee: Employee | null = null;
  isLoading = true;
  isEditMode = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private employeeService: EmployeeService
  ) {}

  ngOnInit(): void {
    this.employeeId = this.route.snapshot.params['id'];
    this.isEditMode = this.router.url.includes('/edit');
    this.loadEmployee();
  }

  private loadEmployee(): void {
    this.employeeService.getEmployeeById(this.employeeId).subscribe({
      next: (employee) => {
        if (employee) {
          this.employee = employee;
        } else {
          this.router.navigate(['/employees']);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading employee:', error);
        this.isLoading = false;
        this.router.navigate(['/employees']);
      }
    });
  }
}