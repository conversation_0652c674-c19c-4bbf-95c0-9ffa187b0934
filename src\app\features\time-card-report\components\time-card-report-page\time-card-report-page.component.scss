﻿.page-container {
  padding: 24px; // py-6 px-4 from mock
  max-width: 1280px; // max-w-7xl
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px; // mb-6
}

.page-title {
  font-size: 1.5rem; // text-2xl
  font-weight: bold; // font-bold
  color: #111827; // text-gray-900
}

mat-button-toggle-group {
  border: 1px solid #d1d5db; // border-gray-300
  border-radius: 0.375rem; // rounded-md
  box-shadow: none;

  mat-button-toggle {
    font-size: 0.875rem; // text-sm
    font-weight: 500; // font-medium
    padding: 6px 12px; // px-3 py-1.5
    color: #374151; // text-gray-700
    background-color: white;

    &.mat-button-toggle-checked {
      background-color: #e0f2fe; // bg-blue-100
      color: #1d4ed8; // text-blue-800
    }

    &:not(.mat-button-toggle-checked):hover {
        background-color: #f9fafb; // hover:bg-gray-100
    }

    mat-icon {
      margin-right: 4px;
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

// When dashboard is active, table header is visible but content hidden by its own expansion panel logic
// This class ensures the component itself is still in the DOM flow if needed.
.partially-hidden {
  // No specific styles needed here if the component handles its own content visibility.
  // The mock shows the data-table-view header even when dashboard is selected.
  // So, app-data-table-view should always be rendered, and its internal mat-expansion-panel
  // will control content visibility.
}

// If we want to truly hide the data table view component when dashboard is active:
// app-data-table-view[hidden] { display: none !important; }
// But the mock implies its header is always visible, just potentially collapsed.