﻿export interface ReportParameters {
  reportDate: string; // or Date
  supervisors: string[];
  teamLeads: string[];
  departments: string[];
  employmentStatus: string[];
  layoutOptions: LayoutOptions;
}

export interface LayoutOptions {
  showYTD: boolean;
  showQuarterly: boolean;
  showCurrent: boolean;
  showMonthly: boolean;
}

export const initialReportParameters: ReportParameters = {
  reportDate: '2/28/25', // Consider using Date objects and a date pipe for display
  supervisors: ['<PERSON>', '<PERSON>'],
  teamLeads: ['<PERSON>', '<PERSON>'],
  departments: ['Engineering', 'Human Resources', 'Marketing'],
  employmentStatus: ['Full-time', 'Part-time', 'Contractor'],
  layoutOptions: {
    showYTD: true,
    showQuarterly: true,
    showCurrent: true,
    showMonthly: false,
  },
};