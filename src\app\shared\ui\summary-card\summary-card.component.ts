﻿import { Component, Input } from '@angular/core';
import { CommonModule, DecimalPipe, PercentPipe } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-summary-card',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule, DecimalPipe, PercentPipe],
  templateUrl: './summary-card.component.html',
  styleUrls: ['./summary-card.component.scss'],
})
export class SummaryCardComponent {
  @Input() title: string = '';
  @Input() value: string | number = '';
  @Input() iconName: string = ''; // Material Icon name
  @Input() trendPercentage?: number; // e.g., 0.125 for 12.5%
  @Input() trendDirection?: 'up' | 'down';
  @Input() trendText?: string = 'from last month';
  @Input() iconBgColorClass?: string; // e.g., 'bg-green-100'
  @Input() iconColorClass?: string; // e.g., 'text-green-600'
  @Input() valueType: 'currency' | 'percent' | 'number' = 'number';

  get valueDisplay(): string {
    if (this.valueType === 'currency') {
      return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Number(this.value));
    }
    if (this.valueType === 'percent') {
      return new PercentPipe('en-US').transform(Number(this.value) / 100, '1.1-1');
    }
    return String(this.value);
  }
}