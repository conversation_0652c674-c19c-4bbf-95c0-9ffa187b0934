﻿.dashboard-panel {
  margin-bottom: 24px; // mb-6
  background-color: white;
  border: 1px solid #e0e0e0; // border-gray-200
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); // shadow-sm

  .mat-expansion-panel-header {
    &:hover {
      background-color: #f9fafb; // hover:bg-gray-50
    }
  }

  .mat-expansion-panel-header-title mat-icon {
    margin-right: 8px;
    color: #6b7280; // text-gray-500
  }
}

.dashboard-content {
  // No extra padding needed if tabs handle it
}

.tab-content-padding {
    padding: 24px; // p-6
}

.summary-cards-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 16px; // gap-4
  margin-bottom: 24px; // mb-6

  @media (min-width: 768px) { // md:grid-cols-2
    grid-template-columns: repeat(2, 1fr);
  }
  @media (min-width: 1024px) { // lg:grid-cols-4
    grid-template-columns: repeat(4, 1fr);
  }
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 24px; // gap-6
  margin-bottom: 24px; // mb-6

  @media (min-width: 1024px) { // lg:grid-cols-2
    grid-template-columns: repeat(2, 1fr);
  }
}

.chart-card, .employee-type-card {
  padding: 0; // mat-card has its own padding, adjust if needed for app-chart-wrapper
  // app-chart-wrapper itself has padding for the chart area
  // The mock had p-6 on the card, then chart background.
  // For simplicity, let mat-card handle outer padding and chart-wrapper inner.
  // If specific p-6 is needed for card content around chart-wrapper:
  // .mat-card-content { padding: 24px !important; }
}

// Tab styling to match mock
::ng-deep .dashboard-panel {
    .mat-tab-labels {
        padding: 0 16px; // px-4 for the tab nav
    }
    .mat-tab-label {
        padding: 16px 24px !important; // py-4 px-6
        font-size: 0.875rem; // text-sm
        font-weight: 500; // font-medium
        color: #6b7280; // text-gray-500
        opacity: 1 !important;
        min-width: auto !important;

        &:hover {
            color: #374151; // hover:text-gray-700
            // hover:border-gray-300 is handled by mat-ink-bar focus
        }
        &.mat-tab-label-active {
            color: #2563eb !important; // text-blue-600
        }
    }
    .mat-ink-bar {
        background-color: #2563eb !important; // border-blue-500
        height: 2px !important;
    }
    .mat-tab-header {
        border-bottom: 1px solid #e5e7eb; // border-b border-gray-200
    }
}