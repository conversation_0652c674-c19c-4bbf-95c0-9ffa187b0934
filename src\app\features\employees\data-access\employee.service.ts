import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { Employee, Department, EmploymentType } from '../models/employee.model';

@Injectable({
  providedIn: 'root'
})
export class EmployeeService {
  private mockEmployees: Employee[] = [
    {
      id: 'emp-001',
      firstName: 'Alice',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      phoneNumber: '(*************',
      jobTitle: 'Software Engineer',
      department: 'Technology',
      status: 'Active',
      hireDate: '2022-01-15',
      avatarUrl: 'https://placehold.co/100x100.png?text=AS',
      salary: 90000,
      performanceScore: 92,
      engagementScore: 85,
      yearsOfService: 2,
      lastReviewDate: '2023-12-01',
      manager: '<PERSON>',
      location: 'New York',
      locationType: 'Hybrid',
      employeeType: 'Full-time',
      skills: ['React', 'Node.js', 'TypeScript'],
      notes: 'High performer, consistently delivers excellent results.',
      dateOfBirth: '1990-05-20'
    },
    {
      id: 'emp-002',
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      phoneNumber: '(*************',
      jobTitle: 'Product Manager',
      department: 'Product',
      status: 'Active',
      hireDate: '2021-06-01',
      avatarUrl: 'https://placehold.co/100x100.png?text=BJ',
      salary: 110000,
      performanceScore: 88,
      engagementScore: 90,
      yearsOfService: 3,
      lastReviewDate: '2024-01-10',
      manager: 'Carol White',
      location: 'San Francisco',
      locationType: 'Office',
      employeeType: 'Full-time',
      skills: ['Agile', 'Roadmap Planning', 'User Research'],
      notes: 'Strong leadership skills, actively contributes to team morale.',
      dateOfBirth: '1985-11-10'
    },
    {
      id: 'emp-003',
      firstName: 'Charlie',
      lastName: 'Brown',
      email: '<EMAIL>',
      phoneNumber: '(*************',
      jobTitle: 'UX Designer',
      department: 'Design',
      status: 'On Leave',
      hireDate: '2023-03-10',
      avatarUrl: 'https://placehold.co/100x100.png?text=CB',
      salary: 85000,
      performanceScore: 90,
      engagementScore: 80,
      yearsOfService: 1,
      lastReviewDate: '2023-09-01',
      manager: 'David Green',
      location: 'Remote',
      locationType: 'Remote',
      employeeType: 'Full-time',
      skills: ['Figma', 'User Testing', 'Prototyping'],
      notes: 'Currently on parental leave. Expected back on 2024-09-01.',
      dateOfBirth: '1992-02-29'
    },
    {
      id: 'emp-004',
      firstName: 'Diana',
      middleName: 'Grace',
      lastName: 'Miller',
      email: '<EMAIL>',
      phoneNumber: '(*************',
      jobTitle: 'Marketing Specialist',
      department: 'Marketing',
      status: 'Active',
      hireDate: '2022-08-20',
      avatarUrl: 'https://placehold.co/100x100.png?text=DM',
      salary: 75000,
      performanceScore: 85,
      engagementScore: 88,
      yearsOfService: 1.5,
      lastReviewDate: '2024-02-15',
      manager: 'Emily Clark',
      location: 'Chicago',
      locationType: 'Office',
      employeeType: 'Part-time',
      skills: ['SEO', 'Content Creation', 'Social Media'],
      notes: 'Creative and proactive in campaign strategies.',
      dateOfBirth: '1995-07-15'
    },
    {
      id: 'emp-005',
      firstName: 'Ethan',
      lastName: 'Davis',
      email: '<EMAIL>',
      phoneNumber: '(*************',
      jobTitle: 'Data Analyst',
      department: 'Analytics',
      status: 'Terminated',
      hireDate: '2020-11-05',
      avatarUrl: 'https://placehold.co/100x100.png?text=ED',
      salary: 95000,
      performanceScore: 70,
      engagementScore: 60,
      yearsOfService: 3,
      lastReviewDate: '2023-05-01',
      manager: 'Frank Wright',
      location: 'Austin',
      locationType: 'Hybrid',
      employeeType: 'Contractor',
      skills: ['SQL', 'Python', 'Tableau'],
      notes: 'Employment terminated on 2023-11-15 due to company restructuring.',
      dateOfBirth: '1988-10-01'
    }
  ];

  private mockDepartments: Department[] = [
    { id: 'dept-tech', name: 'Technology', description: 'Software development and technical infrastructure', employeeCount: 45, color: '#C3E5AE' },
    { id: 'dept-prod', name: 'Product', description: 'Product strategy and development lifecycle', employeeCount: 25, color: '#A7C5EB' },
    { id: 'dept-design', name: 'Design', description: 'Product and graphic design', employeeCount: 18, color: '#FFD6E0' },
    { id: 'dept-marketing', name: 'Marketing', description: 'Brand, communications, and customer acquisition', employeeCount: 22, color: '#A7C5EB' },
    { id: 'dept-analytics', name: 'Analytics', description: 'Data analysis and reporting', employeeCount: 15, color: '#BEE3F8' },
    { id: 'dept-hr', name: 'Human Resources', description: 'Employee relations and recruitment', employeeCount: 10, color: '#D6CDEA' },
    { id: 'dept-finance', name: 'Finance', description: 'Accounting, budgeting, and financial planning', employeeCount: 12, color: '#FFEE93' },
    { id: 'dept-it', name: 'IT', description: 'Internal IT support and infrastructure', employeeCount: 8, color: '#C6D8AF' }
  ];

  private mockEmploymentTypes: EmploymentType[] = [
    { id: 'emptype-ft', name: 'Full-time', description: 'Standard 40-hour work week with benefits', employeeCount: 85 },
    { id: 'emptype-pt', name: 'Part-time', description: 'Less than 40 hours per week', employeeCount: 12 },
    { id: 'emptype-co', name: 'Contractor', description: 'External workers on contract basis', employeeCount: 28 },
    { id: 'emptype-in', name: 'Intern', description: 'Temporary position for students or trainees', employeeCount: 5 }
  ];

  constructor() {}

  getEmployees(): Observable<Employee[]> {
    // Simulate API delay
    return of(this.mockEmployees).pipe(delay(800));
  }

  getEmployeeById(id: string): Observable<Employee | undefined> {
    const employee = this.mockEmployees.find(emp => emp.id === id);
    return of(employee).pipe(delay(500));
  }

  getDepartments(): Observable<Department[]> {
    return of(this.mockDepartments).pipe(delay(500));
  }

  getEmploymentTypes(): Observable<EmploymentType[]> {
    return of(this.mockEmploymentTypes).pipe(delay(500));
  }

  addEmployee(employee: Partial<Employee>): Observable<Employee> {
    const newEmployee: Employee = {
      id: `emp-${Date.now().toString().slice(-4)}`,
      status: 'Active',
      hireDate: new Date().toISOString().split('T')[0],
      ...employee
    } as Employee;
    
    this.mockEmployees.push(newEmployee);
    return of(newEmployee).pipe(delay(800));
  }

  updateEmployee(id: string, updates: Partial<Employee>): Observable<Employee | undefined> {
    const index = this.mockEmployees.findIndex(emp => emp.id === id);
    
    if (index === -1) {
      return of(undefined).pipe(delay(500));
    }
    
    const updatedEmployee = {
      ...this.mockEmployees[index],
      ...updates
    };
    
    this.mockEmployees[index] = updatedEmployee;
    return of(updatedEmployee).pipe(delay(800));
  }

  deleteEmployee(id: string): Observable<boolean> {
    const initialLength = this.mockEmployees.length;
    this.mockEmployees = this.mockEmployees.filter(emp => emp.id !== id);
    
    return of(this.mockEmployees.length < initialLength).pipe(delay(800));
  }
}