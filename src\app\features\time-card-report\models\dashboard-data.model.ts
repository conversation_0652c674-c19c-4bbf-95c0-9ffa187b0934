﻿export interface SummaryMetric {
  title: string;
  value: string | number;
  valueType?: 'currency' | 'percent' | 'number';
  iconName: string;
  trendPercentage?: number;
  trendDirection?: 'up' | 'down';
  trendText?: string;
  iconBgColorClass?: string;
  iconColorClass?: string;
}

export interface ChartDataPoint {
  name: string;
  value: number;
}

export interface MonthlyTrendDataPoint {
    name: string; // Month
    value: number; // Hours
}

export interface EmployeeTypeDistribution {
  type: string;
  count: number;
  colorClass?: string; // For legend, e.g. 'bg-blue-500'
}