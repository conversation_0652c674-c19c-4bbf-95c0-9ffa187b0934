<div class="settings-container">
  <header class="settings-header">
    <div class="header-content">
      <h1>Staffing Agencies</h1>
    </div>
    <button class="add-button" routerLink="add">
      <span class="material-icons">add</span>
      Add Agency
    </button>
  </header>

  <!-- Agencies List -->
  <div class="agencies-grid">
    <div class="agency-card" *ngFor="let agency of agencies">
      <div class="agency-header">
        <h3>{{ agency.name }}</h3>
        <span class="status-badge" [class.active]="agency.status === 'Active'">
          {{ agency.status }}
        </span>
      </div>

      <div class="agency-content">
        <div class="info-section">
          <h4>Primary Contact</h4>
          <div class="contact-info">
            <p class="contact-name">{{ agency.primaryContact.name }}</p>
            <p class="contact-title">{{ agency.primaryContact.title }}</p>
            <p class="contact-detail">
              <span class="material-icons">email</span>
              {{ agency.primaryContact.email }}
            </p>
            <p class="contact-detail">
              <span class="material-icons">phone</span>
              {{ agency.primaryContact.phone }}
            </p>
          </div>
        </div>

        <div class="info-section">
          <h4>Invoice Submission</h4>
          <p>{{ agency.invoiceSubmission.frequency }}</p>
          <p class="last-invoice">
            <span class="material-icons">receipt</span>
            Last Invoice: {{ agency.invoiceSubmission.lastInvoice }}
          </p>
        </div>
      </div>

      <div class="agency-footer">
        <button class="edit-button">
          <span class="material-icons">edit</span>
          Edit
        </button>
      </div>
    </div>
  </div>

  <!-- Invoice Schema Layouts -->
  <div class="section-container">
    <div class="section-header">
      <div class="section-title">
        <span class="material-icons">description</span>
        <h2>Invoice Schema Layouts</h2>
      </div>
      <button class="add-schema-button">
        <span class="material-icons">add</span>
        Add Schema
      </button>
    </div>

    <div class="schema-table">
      <table>
        <thead>
          <tr>
            <th>Schema Name</th>
            <th>Agencies</th>
            <th>Created</th>
            <th>Expires</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let schema of schemas">
            <td>
              <div class="schema-name">
                <strong>{{ schema.name }}</strong>
                <span class="schema-description">{{ schema.description }}</span>
              </div>
            </td>
            <td>{{ schema.agencies }}</td>
            <td>{{ schema.created }}</td>
            <td>{{ schema.expires }}</td>
            <td>
              <span class="status-badge" [class.active]="schema.status === 'Active'" [class.expiring]="schema.status === 'Expiring Soon'">
                {{ schema.status }}
              </span>
            </td>
            <td>
              <button class="icon-button">
                <span class="material-icons">edit</span>
              </button>
              <button class="icon-button">
                <span class="material-icons">more_vert</span>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>