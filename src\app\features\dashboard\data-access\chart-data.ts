// Chart Data Types
export interface ChartDataPoint {
  name: string;
  value: number;
  fill: string;
}

export interface MonthlyHoursDataPoint {
  month: string;
  hours: number;
  fill: string;
}

export interface EmployeeTypeDataPoint {
  type: string;
  count: number;
  fill: string;
}

// Chart Colors
const chartColors = {
  primary: 'hsl(215, 70%, 60%)',
  secondary: 'hsl(280, 60%, 65%)',
  tertiary: 'hsl(150, 60%, 55%)',
  quaternary: 'hsl(45, 70%, 60%)',
  quinary: 'hsl(330, 65%, 65%)'
};

// Department Chart Data
export const departmentChartData: ChartDataPoint[] = [
  { name: 'Engineering', value: 84.2, fill: chartColors.primary },
  { name: 'Marketing', value: 92.0, fill: chartColors.secondary },
  { name: 'Design', value: 76.3, fill: chartColors.tertiary },
  { name: 'Finance', value: 88.5, fill: chartColors.quaternary },
  { name: 'Product', value: 79.8, fill: chartColors.quinary }
];

// Monthly Hours Trend Data
export const monthlyHoursTrendData: MonthlyHoursDataPoint[] = [
  { month: 'Sep', hours: 1250, fill: chartColors.primary },
  { month: 'Oct', hours: 1300, fill: chartColors.primary },
  { month: 'Nov', hours: 1180, fill: chartColors.primary },
  { month: 'Dec', hours: 980, fill: chartColors.primary },
  { month: 'Jan', hours: 1420, fill: chartColors.primary },
  { month: 'Feb', hours: 1560, fill: chartColors.primary }
];

// Employee Type Distribution Data
export const employeeTypeChartData: EmployeeTypeDataPoint[] = [
  { type: 'Full-time', count: 28, fill: chartColors.primary },
  { type: 'Part-time', count: 7, fill: chartColors.secondary },
  { type: 'Contractor', count: 12, fill: chartColors.tertiary }
];

// Chart Configuration Objects
export const chartConfigs = {
  department: {
    type: 'bar' as const,
    options: {
      indexAxis: 'y' as const,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        x: {
          beginAtZero: true,
          max: 100,
          title: {
            display: true,
            text: 'Utilization %'
          }
        }
      }
    }
  },
  monthlyHours: {
    type: 'line' as const,
    options: {
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Hours'
          }
        }
      }
    }
  },
  employeeType: {
    type: 'doughnut' as const,
    options: {
      plugins: {
        legend: {
          position: 'bottom' as const
        }
      }
    }
  }
};