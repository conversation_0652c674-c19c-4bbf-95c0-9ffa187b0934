import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css'],
  standalone: true,
  imports: [FormsModule, CommonModule]
})
export class HeaderComponent {
  user = {
    name: '<PERSON>',
    role: 'Administrator',
    avatar: 'https://placehold.co/100x100.png'
  };
  
  isSearchExpanded = false;
  searchQuery = '';
  
  toggleSearch(): void {
    this.isSearchExpanded = !this.isSearchExpanded;
    if (this.isSearchExpanded) {
      setTimeout(() => {
        document.getElementById('search-input')?.focus();
      }, 100);
    }
  }
  
  search(): void {
    console.log('Searching for:', this.searchQuery);
    // Implement search functionality
  }
}
