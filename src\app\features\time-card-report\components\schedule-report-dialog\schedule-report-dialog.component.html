﻿<div class="dialog-header bg-blue-600 text-white">
  <h2 mat-dialog-title>Schedule Report</h2>
  <button mat-icon-button (click)="onCancel()" aria-label="Close dialog">
    <mat-icon>close</mat-icon>
  </button>
</div>

<mat-dialog-content class="dialog-content" [formGroup]="scheduleForm">
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Report Name</mat-label>
    <input matInput formControlName="reportName" placeholder="Enter report name">
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Frequency</mat-label>
    <mat-select formControlName="frequency">
      <mat-option value="daily">Daily</mat-option>
      <mat-option value="weekly">Weekly</mat-option>
      <mat-option value="biweekly">Bi-weekly</mat-option>
      <mat-option value="monthly">Monthly</mat-option>
      <mat-option value="quarterly">Quarterly</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width" *ngIf="scheduleForm.get('frequency')?.value === 'monthly'">
    <mat-label>Day of Month</mat-label>
    <mat-select formControlName="dayOfMonth">
      <mat-option *ngFor="let day of daysInMonth" [value]="day">{{ day }}</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Start Date</mat-label>
    <input matInput formControlName="startDate" [matDatepicker]="startDatePicker" placeholder="Choose a date">
    <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
    <mat-datepicker #startDatePicker></mat-datepicker>
  </mat-form-field>

  <div class="form-group">
    <label class="group-label">Delivery Method</label>
    <mat-checkbox formControlName="deliveryEmail">Email Report</mat-checkbox>
    <mat-checkbox formControlName="deliverySaveToDirectory">Save to Reports Directory</mat-checkbox>
  </div>

  <mat-form-field appearance="outline" class="full-width" *ngIf="scheduleForm.get('deliveryEmail')?.value">
    <mat-label>Email Recipients</mat-label>
    <textarea matInput formControlName="emailRecipients" placeholder="Enter email addresses separated by commas" cdkTextareaAutosize cdkAutosizeMinRows="2"></textarea>
  </mat-form-field>

  <div class="form-group">
    <label class="group-label">Report Format</label>
    <mat-radio-group formControlName="reportFormat" class="radio-group">
      <mat-radio-button value="pdf">PDF</mat-radio-button>
      <mat-radio-button value="excel">Excel</mat-radio-button>
      <mat-radio-button value="csv">CSV</mat-radio-button>
    </mat-radio-group>
  </div>

  <div class="form-group">
    <mat-checkbox formControlName="includeCurrentParameters">Include current filter parameters</mat-checkbox>
  </div>

</mat-dialog-content>

<mat-dialog-actions align="end" class="dialog-actions">
  <button mat-stroked-button (click)="onCancel()">Cancel</button>
  <button mat-raised-button color="primary" (click)="onSave()" [disabled]="scheduleForm.invalid">
    <mat-icon>notifications_none</mat-icon> Schedule Report
  </button>
</mat-dialog-actions>