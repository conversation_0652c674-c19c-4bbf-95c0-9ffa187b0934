﻿.summary-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .metric-title {
    font-size: 0.875rem; // text-sm
    font-weight: 500; // font-medium
    color: #6b7280; // text-gray-500
    margin: 0 0 4px 0;
  }

  .metric-value {
    font-size: 1.5rem; // text-2xl
    font-weight: bold; // font-bold
    color: #111827; // text-gray-900
    margin: 0;
  }

  .metric-icon-wrapper {
    padding: 8px; // p-2
    border-radius: 0.375rem; // rounded-md
    display: inline-flex; // For icon centering
  }

  .metric-icon {
    width: 24px; // h-6
    height: 24px; // w-6
    font-size: 24px;
  }

  .trend-info {
    margin-top: 8px; // mt-2
    display: flex;
    align-items: center;
    font-size: 0.875rem; // text-sm
  }

  .trend-icon {
    width: 16px; // h-4
    height: 16px; // w-4
    font-size: 16px;
    margin-right: 4px; // mr-1
  }

  .trend-percentage {
    font-weight: 500; // font-medium
  }

  .trend-text {
    color: #6b7280; // text-gray-500
    margin-left: 4px; // ml-1
  }

  // Color utility classes (should ideally be part of a global style or theme)
  .bg-blue-100 { background-color: #dbeafe; }
  .text-blue-600 { color: #2563eb; }
  .bg-green-100 { background-color: #d1fae5; }
  .text-green-600 { color: #059669; }
  .text-green-500 { color: #10b981; }
  .bg-yellow-100 { background-color: #fef3c7; }
  .text-yellow-600 { color: #d97706; }
  .text-red-500 { color: #ef4444; }
  .text-red-600 { color: #dc2626; }
}