<app-employee-container title="Employees" subtitle="Manage and view your company's employees." (addEmployee)="onAddEmployee()">
  <app-employee-filters (search)="onSearch($event)" (statusChange)="onStatusChange($event)" (departmentChange)="onDepartmentChange($event)"></app-employee-filters>

  <div class="employee-list">
    <app-employee-card
      *ngFor="let employee of filteredEmployees"
      [name]="employee.name"
      [status]="employee.status"
      [statusClass]="employee.statusClass"
      [title]="employee.title"
      [email]="employee.email"
      [phone]="employee.phone"
      [location]="employee.location"
      [department]="employee.department"
      [employmentType]="employee.employmentType"
    ></app-employee-card>
  </div>
</app-employee-container>
