﻿import { Component, EventEmitter, Input, OnInit, Output, ViewChild, OnChanges, SimpleChanges, AfterViewInit } from '@angular/core';
import { CommonModule, CurrencyPipe, DecimalPipe, PercentPipe } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatPaginator, MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatProgressBarModule } from '@angular/material/progress-bar';

import { EmployeeTimeCard } from '../../models/employee.model';

@Component({
  selector: 'app-data-table-view',
  standalone: true,
  imports: [
    CommonModule, CurrencyPipe, DecimalPipe, PercentPipe,
    MatExpansionModule, MatIconModule, MatFormFieldModule, MatInputModule, MatButtonModule,
    MatTableModule, MatSortModule, MatPaginatorModule, MatProgressBarModule
  ],
  templateUrl: './data-table-view.component.html',
  styleUrls: ['./data-table-view.component.scss'],
})
export class DataTableViewComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() employeeData: EmployeeTimeCard[] = [];
  @Output() searchChanged = new EventEmitter<string>();
  @Output() commentsRequested = new EventEmitter<EmployeeTimeCard>();

  isExpanded = true;
  displayedColumns: string[] = [
    'lastName', 'firstName', 'id', 'employeeType', 'department',
    'supervisor', 'utilization', 'billable', 'nonBillable',
    'totalHours', 'amount', 'actions'
  ];
  dataSource: MatTableDataSource<EmployeeTimeCard> = new MatTableDataSource<EmployeeTimeCard>();

  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  totalDataLength = 0;
  pageSize = 5; // Initial page size

  constructor() {}

  ngOnInit(): void {
    this.dataSource.data = this.employeeData;
    this.totalDataLength = this.employeeData.length;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['employeeData']) {
      this.dataSource.data = this.employeeData;
      this.totalDataLength = this.employeeData.length;
      if (this.paginator) { // Reset paginator if data changes
        this.paginator.firstPage();
      }
    }
  }

  ngAfterViewInit(): void {
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
    // Custom filter predicate if needed for more complex filtering
    this.dataSource.filterPredicate = (data: EmployeeTimeCard, filter: string) => {
        const dataStr = Object.values(data).join(' ').toLowerCase();
        return dataStr.includes(filter);
    };
  }

  onSearch(value: string): void {
    const filterValue = value.trim().toLowerCase();
    this.dataSource.filter = filterValue;
    this.searchChanged.emit(filterValue);

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  viewComments(employee: EmployeeTimeCard): void {
    this.commentsRequested.emit(employee);
    alert(`View comments for ${employee.firstName} ${employee.lastName} (ID: ${employee.id})`);
    // In a real app, this would open a dialog or navigate to a comments view
  }

  handlePageEvent(event: PageEvent): void {
    this.pageSize = event.pageSize;
    // Paginator handles data slicing automatically if connected to MatTableDataSource
  }

  getUtilizationColorClass(percentage: number): string {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 75) return 'text-blue-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  }

  getUtilizationBgClass(percentage: number): string {
    if (percentage >= 90) return 'bg-green-500';
    if (percentage >= 75) return 'bg-blue-500';
    if (percentage >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  }
}