.sidebar {
  width: 280px;
  height: 100vh;
  background-color: var(--color-card);
  border-right: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  transition: width 0.3s ease;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border);
}

.app-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-primary);
}

.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
}

.sidebar-nav ul, .sidebar-footer ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.sidebar-link, .sidebar-sublink {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: var(--color-text);
  text-decoration: none;
  transition: background-color 0.2s, color 0.2s;
  cursor: pointer;
}

.sidebar-link:hover, .sidebar-sublink:hover {
  background-color: hsla(var(--secondary), 0.8);
  color: var(--color-primary);
  text-decoration: none;
}

.sidebar-link.active {
  background-color: hsla(var(--primary), 0.1);
  color: var(--color-primary);
  font-weight: 500;
}

.sidebar-link.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-icon, .sub-nav-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.expand-icon {
  margin-left: auto;
  font-size: 1.25rem;
  transition: transform 0.2s ease;
}

.sub-nav {
  background-color: hsla(var(--secondary), 0.5);
  overflow: hidden;
}

.sidebar-sublink {
  padding-left: 3.5rem;
  font-size: 0.9rem;
}

.sidebar-footer {
  padding: 1rem 0;
  border-top: 1px solid var(--color-border);
}

@media (max-width: 768px) {
  .sidebar {
    width: 80px;
  }
  
  .sidebar-header {
    text-align: center;
    padding: 1rem 0.5rem;
  }
  
  .app-title {
    display: none;
  }
  
  .nav-label, .sub-nav-label, .expand-icon {
    display: none;
  }
  
  .nav-icon, .sub-nav-icon {
    margin-right: 0;
  }
  
  .sidebar-link, .sidebar-sublink {
    justify-content: center;
    padding: 0.75rem;
  }
  
  .sub-nav {
    position: absolute;
    left: 80px;
    width: 200px;
    border-radius: 0 var(--radius) var(--radius) 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .sidebar-sublink {
    padding-left: 1rem;
  }
}
