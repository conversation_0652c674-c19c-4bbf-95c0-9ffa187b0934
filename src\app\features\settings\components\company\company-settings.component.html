<div class="settings-container">
  <header class="settings-header">
    <div class="header-content">
      <h1>Company Settings</h1>
      <p class="subtitle">Manage your organization's general details.</p>
    </div>
  </header>

  <!-- Company Information -->
  <div class="settings-card">
    <div class="card-header">
      <h2>Company Information</h2>
    </div>

    <form [formGroup]="companyForm" class="settings-form">
      <div class="form-grid">
        <div class="form-group">
          <label for="companyName">Company Name</label>
          <input id="companyName" type="text" formControlName="companyName">
        </div>

        <div class="form-group">
          <label for="taxId">Tax ID</label>
          <input id="taxId" type="text" formControlName="taxId">
        </div>

        <div class="form-group">
          <label for="phone">Phone</label>
          <input id="phone" type="tel" formControlName="phone">
        </div>

        <div class="form-group">
          <label for="email">Email</label>
          <input id="email" type="email" formControlName="email">
        </div>

        <div class="form-group full-width">
          <label for="address">Address</label>
          <input id="address" type="text" formControlName="address">
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="primary-button" (click)="saveChanges()">
          <span class="material-icons">save</span>
          Save Changes
        </button>
      </div>
    </form>
  </div>

  <!-- Department Management -->
  <div class="settings-card">
    <div class="card-header">
      <h2>Department Management</h2>
      <button class="add-button" (click)="addDepartment()">
        <span class="material-icons">add</span>
        Add Department
      </button>
    </div>

    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>Department Name</th>
            <th>Description</th>
            <th>Employees</th>
            <th>Color</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let dept of departments">
            <td>
              <div class="department-name">
                <span class="color-dot" [style.backgroundColor]="dept.color"></span>
                {{ dept.name }}
              </div>
            </td>
            <td>{{ dept.description }}</td>
            <td>{{ dept.employees }}</td>
            <td>
              <div class="color-preview" [style.backgroundColor]="dept.color"></div>
            </td>
            <td>
              <div class="actions">
                <button class="icon-button" (click)="editDepartment(dept)">
                  <span class="material-icons">edit</span>
                </button>
                <button class="icon-button" (click)="deleteDepartment(dept)">
                  <span class="material-icons">delete</span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Employment Types -->
  <div class="settings-card">
    <div class="card-header">
      <h2>Employment Types</h2>
      <button class="add-button" (click)="addEmploymentType()">
        <span class="material-icons">add</span>
        Add Type
      </button>
    </div>

    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>Type Name</th>
            <th>Description</th>
            <th>Employees</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let type of employmentTypes">
            <td>{{ type.name }}</td>
            <td>{{ type.description }}</td>
            <td>{{ type.employees }}</td>
            <td>
              <div class="actions">
                <button class="icon-button" (click)="editEmploymentType(type)">
                  <span class="material-icons">edit</span>
                </button>
                <button class="icon-button" (click)="deleteEmploymentType(type)">
                  <span class="material-icons">delete</span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Job Titles -->
  <div class="settings-card">
    <div class="card-header">
      <h2>Job Titles</h2>
      <button class="add-button" (click)="addJobTitle()">
        <span class="material-icons">add</span>
        Add Job Title
      </button>
    </div>

    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>Title Name</th>
            <th>Department</th>
            <th>Description</th>
            <th>Employees</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let title of jobTitles">
            <td>{{ title.name }}</td>
            <td>
              <span class="department-badge">{{ title.department }}</span>
            </td>
            <td>{{ title.description }}</td>
            <td>{{ title.employees }}</td>
            <td>
              <div class="actions">
                <button class="icon-button" (click)="editJobTitle(title)">
                  <span class="material-icons">edit</span>
                </button>
                <button class="icon-button" (click)="deleteJobTitle(title)">
                  <span class="material-icons">delete</span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>