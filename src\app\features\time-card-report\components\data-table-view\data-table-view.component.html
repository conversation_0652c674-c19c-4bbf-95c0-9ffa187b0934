﻿<mat-expansion-panel [expanded]="isExpanded" (opened)="isExpanded = true" (closed)="isExpanded = false" class="data-table-panel">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <mat-icon>list</mat-icon>
      Employee Time Card Data
    </mat-panel-title>
    <mat-panel-description class="panel-actions">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search employees</mat-label>
        <mat-icon matPrefix>search</mat-icon>
        <input matInput placeholder="Search..." #searchInput (input)="onSearch(searchInput.value)">
      </mat-form-field>
      <button mat-icon-button aria-label="Filter data" class="filter-button">
        <mat-icon>filter_list</mat-icon>
      </button>
    </mat-panel-description>
  </mat-expansion-panel-header>

  <div class="table-container mat-elevation-z0"> <!-- Use mat-elevation-z0 to remove default table shadow if panel provides it -->
    <table mat-table [dataSource]="dataSource" matSort class="employee-table">

      <!-- Column Definitions -->
      <ng-container matColumnDef="lastName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Last Name</th>
        <td mat-cell *matCellDef="let emp">{{ emp.lastName }}</td>
      </ng-container>

      <ng-container matColumnDef="firstName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>First Name</th>
        <td mat-cell *matCellDef="let emp">{{ emp.firstName }}</td>
      </ng-container>

      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef>ID</th>
        <td mat-cell *matCellDef="let emp">{{ emp.id }}</td>
      </ng-container>

      <ng-container matColumnDef="employeeType">
        <th mat-header-cell *matHeaderCellDef>Employee Type</th>
        <td mat-cell *matCellDef="let emp">
          <span class="chip-like bg-blue-100 text-blue-800">{{ emp.employeeType }}</span>
        </td>
      </ng-container>

      <ng-container matColumnDef="department">
        <th mat-header-cell *matHeaderCellDef>Department</th>
        <td mat-cell *matCellDef="let emp">{{ emp.department }}</td>
      </ng-container>

      <ng-container matColumnDef="supervisor">
        <th mat-header-cell *matHeaderCellDef>Supervisor</th>
        <td mat-cell *matCellDef="let emp">{{ emp.supervisor }}</td>
      </ng-container>

      <ng-container matColumnDef="utilization">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Utilization %</th>
        <td mat-cell *matCellDef="let emp">
          <div class="utilization-cell" [ngClass]="getUtilizationColorClass(emp.utilization)">
            {{ emp.utilization | percent:'1.1-1' }}
          </div>
          <mat-progress-bar
            class="utilization-bar"
            mode="determinate"
            [value]="emp.utilization"
            [ngClass]="getUtilizationBgClass(emp.utilization)">
          </mat-progress-bar>
        </td>
      </ng-container>

      <ng-container matColumnDef="billable">
        <th mat-header-cell *matHeaderCellDef>Billable</th>
        <td mat-cell *matCellDef="let emp">{{ emp.billable | number:'1.1-1' }} hrs</td>
      </ng-container>

      <ng-container matColumnDef="nonBillable">
        <th mat-header-cell *matHeaderCellDef>Non-Billable</th>
        <td mat-cell *matCellDef="let emp">{{ emp.nonBillable | number:'1.1-1' }} hrs</td>
      </ng-container>

      <ng-container matColumnDef="totalHours">
        <th mat-header-cell *matHeaderCellDef>Total Hours</th>
        <td mat-cell *matCellDef="let emp">{{ emp.totalHours | number:'1.1-1' }} hrs</td>
      </ng-container>

      <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef>Amount Billed</th>
        <td mat-cell *matCellDef="let emp">{{ emp.amount | currency }}</td>
      </ng-container>

      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef></th>
        <td mat-cell *matCellDef="let emp">
          <button mat-icon-button color="primary" (click)="viewComments(emp)" aria-label="View comments">
            <mat-icon>comment</mat-icon>
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="data-row"></tr>

       <!-- Row shown when there is no matching data. -->
      <tr class="mat-row" *matNoDataRow>
        <td class="mat-cell" [attr.colspan]="displayedColumns.length">
          No data matching the filter "{{ searchInput.value }}"
        </td>
      </tr>
    </table>
  </div>
  <mat-paginator
    [pageSizeOptions]="[5, 10, 20]"
    showFirstLastButtons
    [length]="totalDataLength"
    [pageSize]="pageSize"
    (page)="handlePageEvent($event)"
    aria-label="Select page of employees">
  </mat-paginator>
</mat-expansion-panel>