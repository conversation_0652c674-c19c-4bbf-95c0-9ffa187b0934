﻿.report-parameters-panel {
  margin-bottom: 24px; // mb-6
  background-color: white;
  border: 1px solid #e0e0e0; // border-gray-200
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); // shadow-sm

  .mat-expansion-panel-header {
    &:hover {
      background-color: #f9fafb; // hover:bg-gray-50
    }
  }

  .mat-expansion-panel-header-title,
  .mat-expansion-panel-header-description {
    align-items: center;
  }

  .mat-expansion-panel-header-title mat-icon {
    margin-right: 8px;
    color: #6b7280; // text-gray-500
  }

  .panel-actions {
    justify-content: flex-end; // Aligns buttons to the right
    button {
      font-size: 0.875rem; // text-sm
      color: #2563eb; // text-blue-600
      &:hover {
        color: #1d4ed8; // hover:text-blue-800
      }
      mat-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
        margin-right: 4px;
      }
    }
  }
}

.parameters-content {
  padding: 16px 24px; // p-4 (mock had p-4, this is p-6 essentially)
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 24px; // gap-6

  @media (min-width: 768px) { // md:grid-cols-2
    grid-template-columns: repeat(2, 1fr);
  }
}

.full-width {
  width: 100%;
  margin-bottom: 16px; // mb-4
}

// Style for readonly inputs to look like the mock
::ng-deep .mat-form-field.mat-form-field-appearance-outline .mat-form-field-infix input[readonly] {
    background-color: #f9fafb; // bg-gray-50
    cursor: pointer; // To indicate it's clickable (for opening a selector)
}
::ng-deep .mat-form-field.mat-form-field-appearance-outline .mat-form-field-infix input[readonly]:focus {
    outline: none;
    box-shadow: none;
}


.layout-options-group {
  margin-top: 8px; // From structure
  .group-label {
    display: block;
    font-size: 0.875rem; // text-sm
    font-weight: 500; // font-medium
    color: #374151; // text-gray-700
    margin-bottom: 4px; // mb-1
  }
  .checkbox-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px; // gap-3
  }
  mat-checkbox {
    font-size: 0.875rem; // text-sm
    color: #374151; // text-gray-700
  }
}

.action-buttons-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px; // px-4 py-3
  background-color: #f9fafb; // bg-gray-50
  border-top: 1px solid #e0e0e0; // border-t border-gray-200

  button mat-icon {
    margin-right: 8px;
  }
  .mr-2 {
    margin-right: 12px; // space-x-3
  }
}