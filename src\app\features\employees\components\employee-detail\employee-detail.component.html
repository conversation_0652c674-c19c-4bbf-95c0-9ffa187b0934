<div class="page-container fade-in">
  <div *ngIf="isLoading" class="loading-container">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div *ngIf="!isLoading && employee">
    <app-add-employee *ngIf="isEditMode"></app-add-employee>
    
    <div *ngIf="!isEditMode">
      <header class="page-header">
        <div class="header-content">
          <h1>Employee Details: {{ employee.firstName }} {{ employee.lastName }}</h1>
          <p class="subtitle">View employee information.</p>
        </div>
        <div class="header-actions">
          <button class="edit-button" [routerLink]="['/employees', employeeId, 'edit']">
            <span class="material-icons">edit</span>
            Edit
          </button>
          <button class="cancel-button" routerLink="/employees">
            <span class="material-icons">close</span>
            Close
          </button>
        </div>
      </header>

      <div class="employee-details">
        <div class="details-section">
          <h2>Basic Information</h2>
          <div class="details-grid">
            <div class="detail-item">
              <label>Name</label>
              <p>{{ employee.firstName }} {{ employee.middleName }} {{ employee.lastName }}</p>
            </div>
            <div class="detail-item">
              <label>Email</label>
              <p>{{ employee.email }}</p>
            </div>
            <div class="detail-item">
              <label>Phone</label>
              <p>{{ employee.phoneNumber || 'N/A' }}</p>
            </div>
          </div>
        </div>

        <div class="details-section">
          <h2>Employment Details</h2>
          <div class="details-grid">
            <div class="detail-item">
              <label>Job Title</label>
              <p>{{ employee.jobTitle }}</p>
            </div>
            <div class="detail-item">
              <label>Department</label>
              <p>{{ employee.department }}</p>
            </div>
            <div class="detail-item">
              <label>Status</label>
              <p>{{ employee.status }}</p>
            </div>
            <div class="detail-item">
              <label>Employment Type</label>
              <p>{{ employee.employeeType }}</p>
            </div>
            <div class="detail-item">
              <label>Location</label>
              <p>{{ employee.location }} ({{ employee.locationType }})</p>
            </div>
            <div class="detail-item">
              <label>Hire Date</label>
              <p>{{ employee.hireDate | date }}</p>
            </div>
            <div class="detail-item">
              <label>Manager</label>
              <p>{{ employee.manager || 'N/A' }}</p>
            </div>
            <div class="detail-item">
              <label>Salary</label>
              <p>{{ employee.salary ? ('$' + employee.salary.toLocaleString()) : 'N/A' }}</p>
            </div>
          </div>
        </div>

        <div class="details-section" *ngIf="employee.notes">
          <h2>Additional Information</h2>
          <div class="detail-item">
            <label>Notes</label>
            <p>{{ employee.notes }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>