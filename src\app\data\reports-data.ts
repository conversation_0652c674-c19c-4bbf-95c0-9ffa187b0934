// Data interfaces
export interface UtilizationData {
  name: string;
  value: number;
  fill: string;
}

export interface BillableHoursData {
  month: string;
  hours: number;
  fill: string;
}

export interface EmployeeTypeData {
  type: string;
  count: number;
  fill: string;
}

export interface LayoutOptions {
  showYTD: boolean;
  showQuarterly: boolean;
  showCurrent: boolean;
  showMonthly: boolean;
}

export interface Parameters {
  reportDate: string;
  supervisors: string[];
  teamLeads: string[];
  departments: string[];
  employmentStatus: string[];
  layoutOptions: LayoutOptions;
}

export interface TrendData {
  value: number;
  trend: number;
  trendDirection: 'up' | 'down';
}

export interface SummaryCardData {
  billableHours: TrendData;
  nonBillableHours: TrendData;
  utilization: TrendData;
  revenue: TrendData;
}

export interface EmployeeComment {
  id: number;
  text: string;
  date: string;
  author: string;
}

export interface EmployeeData {
  id: string;
  firstName: string;
  lastName: string;
  employeeType: string;
  department: string;
  supervisor: string;
  teamLead: string;
  utilization: number;
  billable: number;
  nonBillable: number;
  totalHours: number;
  amount: number;
  comments: EmployeeComment[];
}

// Mock data
export const employeesData: EmployeeData[] = [
  {
    id: 'EMP001',
    firstName: 'John',
    lastName: 'Smith',
    employeeType: 'Full-time',
    department: 'Engineering',
    supervisor: 'Jane Doe',
    teamLead: 'Mike Johnson',
    utilization: 87.5,
    billable: 35.0,
    nonBillable: 5.0,
    totalHours: 40.0,
    amount: 5250.00,
    comments: [
      {
        id: 1,
        date: '2025-02-15',
        text: 'Employee missed timecard submission deadline twice this month. Follow up required.',
        author: 'Jane Doe'
      },
      {
        id: 2,
        date: '2025-02-22',
        text: 'Discussed timecard expectations. Employee will set calendar reminders.',
        author: 'Jane Doe'
      }
    ]
  },
  {
    id: 'EMP002',
    firstName: 'Sarah',
    lastName: 'Johnson',
    employeeType: 'Part-time',
    department: 'Marketing',
    supervisor: 'Bob Williams',
    teamLead: 'Lisa Chen',
    utilization: 92.0,
    billable: 20.0,
    nonBillable: 2.0,
    totalHours: 22.0,
    amount: 3300.00,
    comments: []
  },
  {
    id: 'EMP003',
    firstName: 'David',
    lastName: 'Rodriguez',
    employeeType: 'Contractor',
    department: 'Engineering',
    supervisor: 'Jane Doe',
    teamLead: 'Mike Johnson',
    utilization: 100.0,
    billable: 40.0,
    nonBillable: 0.0,
    totalHours: 40.0,
    amount: 6800.00,
    comments: []
  },
  {
    id: 'EMP004',
    firstName: 'Emily',
    lastName: 'Chen',
    employeeType: 'Full-time',
    department: 'Design',
    supervisor: 'Michael Brown',
    teamLead: 'Sarah Wilson',
    utilization: 76.3,
    billable: 25.5,
    nonBillable: 14.5,
    totalHours: 40.0,
    amount: 3825.00,
    comments: []
  },
  {
    id: 'EMP005',
    firstName: 'James',
    lastName: 'Williams',
    employeeType: 'Full-time',
    department: 'Engineering',
    supervisor: 'Jane Doe',
    teamLead: 'Mike Johnson',
    utilization: 65.0,
    billable: 26.0,
    nonBillable: 14.0,
    totalHours: 40.0,
    amount: 3900.00,
    comments: []
  }
];

export const utilizationByDepartmentData: UtilizationData[] = [
  { name: 'Engineering', value: 84.2, fill: 'hsl(215, 70%, 60%)' },
  { name: 'Marketing', value: 92.0, fill: 'hsl(280, 60%, 65%)' },
  { name: 'Design', value: 76.3, fill: 'hsl(150, 60%, 55%)' },
  { name: 'Finance', value: 88.5, fill: 'hsl(45, 70%, 60%)' },
  { name: 'Product', value: 79.8, fill: 'hsl(330, 65%, 65%)' }
];

export const billableHoursTrendData: BillableHoursData[] = [
  { month: 'Sep', hours: 1250, fill: 'hsl(215, 70%, 60%)' },
  { month: 'Oct', hours: 1300, fill: 'hsl(215, 70%, 60%)' },
  { month: 'Nov', hours: 1180, fill: 'hsl(215, 70%, 60%)' },
  { month: 'Dec', hours: 980, fill: 'hsl(215, 70%, 60%)' },
  { month: 'Jan', hours: 1420, fill: 'hsl(215, 70%, 60%)' },
  { month: 'Feb', hours: 1560, fill: 'hsl(215, 70%, 60%)' }
];

export const employeeTypeDistributionData: EmployeeTypeData[] = [
  { type: 'Full-time', count: 28, fill: 'hsl(215, 70%, 60%)' },
  { type: 'Part-time', count: 7, fill: 'hsl(280, 60%, 65%)' },
  { type: 'Contractor', count: 12, fill: 'hsl(150, 60%, 55%)' }
];

export const summaryCardsData: SummaryCardData = {
  billableHours: {
    value: 1560,
    trend: 9.8,
    trendDirection: 'up'
  },
  nonBillableHours: {
    value: 280,
    trend: -12.5,
    trendDirection: 'down'
  },
  utilization: {
    value: 84.8,
    trend: 3.2,
    trendDirection: 'up'
  },
  revenue: {
    value: 234000,
    trend: 8.5,
    trendDirection: 'up'
  }
};

export const initialParameters: Parameters = {
  reportDate: '2025-02-28',
  supervisors: ['Bob Smith', 'Jane Doe'],
  teamLeads: ['Bob Smith', 'Jane Doe'],
  departments: ['Engineering', 'Human Resources', 'Marketing'],
  employmentStatus: ['Full-time', 'Part-time', 'Contractor'],
  layoutOptions: {
    showYTD: true,
    showQuarterly: true,
    showCurrent: true,
    showMonthly: false
  }
};