﻿export interface ScheduleReportConfig {
  reportName: string;
  frequency: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'quarterly';
  dayOfMonth?: number; // For monthly
  dayOfWeek?: number; // For weekly/biweekly
  startDate: string; // ISO Date string
  deliveryEmail: boolean;
  deliverySaveToDirectory: boolean;
  emailRecipients: string[];
  reportFormat: 'pdf' | 'excel' | 'csv';
  includeCurrentParameters: boolean;
}