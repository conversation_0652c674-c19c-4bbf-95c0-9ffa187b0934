﻿import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChartWrapperComponent } from '../../../../shared/ui/chart-wrapper/chart-wrapper.component';
import { EmployeeTypeDistribution } from '../../models/dashboard-data.model';

@Component({
  selector: 'app-employee-type-distribution',
  standalone: true,
  imports: [CommonModule, ChartWrapperComponent],
  templateUrl: './employee-type-distribution.component.html',
  styleUrls: ['./employee-type-distribution.component.scss'],
})
export class EmployeeTypeDistributionComponent implements OnInit, OnChanges {
  @Input() distributionData: EmployeeTypeDistribution[] = [];

  totalEmployees = 0;
  chartData: { name: string, value: number }[] = [];
  
  // Define colors for the pie chart and legend
  private defaultColors = ['#3b82f6', '#10b981', '#f59e0b', '#6366f1', '#ec4899']; // blue, green, yellow, indigo, pink
  pieChartColorScheme: { domain: string[] } = { domain: this.defaultColors };


  ngOnInit(): void {
    this.processData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['distributionData']) {
      this.processData();
    }
  }

  private processData(): void {
    this.totalEmployees = this.distributionData.reduce((sum, item) => sum + item.count, 0);
    this.chartData = this.distributionData.map(item => ({
      name: item.type,
      value: item.count
    }));
    // Update color scheme if number of types exceeds default colors
    if (this.distributionData.length > this.defaultColors.length) {
        // Simple extension, ideally use a color generation library for more distinct colors
        const additionalColorsNeeded = this.distributionData.length - this.defaultColors.length;
        const extendedColors = [...this.defaultColors];
        for(let i=0; i<additionalColorsNeeded; i++) {
            extendedColors.push(this.generateRandomColor());
        }
        this.pieChartColorScheme = { domain: extendedColors };
    } else {
        this.pieChartColorScheme = { domain: this.defaultColors.slice(0, this.distributionData.length) };
    }
  }

  getPercentage(count: number): number {
    return this.totalEmployees > 0 ? (count / this.totalEmployees) * 100 : 0;
  }

  getColorForType(index: number): string {
    return this.pieChartColorScheme.domain[index % this.pieChartColorScheme.domain.length];
  }

  private generateRandomColor(): string { // Basic random color for demo
    return '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
  }
}