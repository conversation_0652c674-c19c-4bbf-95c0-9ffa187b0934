.settings-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.settings-header h1 {
  font-size: 1.875rem;
  font-weight: 600;
  margin: 0;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.add-button:hover {
  background-color: var(--color-primary-light);
}

/* Agencies Grid */
.agencies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.agency-card {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.agency-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agency-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.agency-content {
  padding: 1.25rem;
}

.info-section {
  margin-bottom: 1.25rem;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-section h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--muted-foreground);
  margin: 0 0 0.75rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.contact-name {
  font-weight: 500;
  margin: 0;
}

.contact-title {
  color: var(--muted-foreground);
  font-size: 0.875rem;
  margin: 0;
}

.contact-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  margin: 0;
}

.contact-detail .material-icons {
  font-size: 1rem;
  color: var(--muted-foreground);
}

.last-invoice {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--muted-foreground);
  margin: 0.5rem 0 0;
}

.agency-footer {
  padding: 1rem 1.25rem;
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
}

.edit-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: none;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s;
}

.edit-button:hover {
  background-color: hsla(var(--secondary), 0.8);
}

/* Schema Section */
.section-container {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-title h2 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.section-title .material-icons {
  color: var(--muted-foreground);
}

.add-schema-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: none;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s;
}

.add-schema-button:hover {
  background-color: hsla(var(--secondary), 0.8);
}

/* Schema Table */
.schema-table {
  width: 100%;
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  text-align: left;
  padding: 1rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--muted-foreground);
  border-bottom: 1px solid var(--color-border);
}

td {
  padding: 1rem 1.25rem;
  font-size: 0.875rem;
  border-bottom: 1px solid var(--color-border);
}

.schema-name {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.schema-description {
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgb(16, 185, 129);
}

.status-badge.expiring {
  background-color: rgba(245, 158, 11, 0.1);
  color: rgb(245, 158, 11);
}

.icon-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  border-radius: 9999px;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.2s;
}

.icon-button:hover {
  background-color: hsla(var(--secondary), 0.8);
  color: var(--color-primary);
}

@media (max-width: 768px) {
  .settings-container {
    padding: 1rem;
  }

  .agencies-grid {
    grid-template-columns: 1fr;
  }

  .settings-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .add-button {
    width: 100%;
    justify-content: center;
  }
}