﻿.data-table-panel {
  // margin-bottom: 24px; // mb-6 (Handled by page if this is the last item)
  background-color: white;
  border: 1px solid #e0e0e0; // border-gray-200
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); // shadow-sm

  .mat-expansion-panel-header {
    &:hover {
      background-color: #f9fafb; // hover:bg-gray-50
    }
  }

  .mat-expansion-panel-header-title,
  .mat-expansion-panel-header-description {
    align-items: center;
  }

  .mat-expansion-panel-header-title mat-icon {
    margin-right: 8px;
    color: #6b7280; // text-gray-500
  }

  .panel-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end; // Aligns items to the right
    gap: 16px; // space-x-4
    width: 100%; // Ensure it takes space to push title left

    .search-field {
      // width: 250px; // Adjust as needed
      font-size: 0.875rem; // sm:text-sm
      ::ng-deep .mat-form-field-wrapper {
        padding-bottom: 0; // Remove extra space below
      }
      ::ng-deep .mat-form-field-infix {
        padding: 0.5em 0 !important; // py-2
        border-top-width: 0.6em; // Adjust for vertical centering
      }
      ::ng-deep .mat-form-field-outline-start,
      ::ng-deep .mat-form-field-outline-end,
      ::ng-deep .mat-form-field-outline-gap {
        border-radius: 0.375rem !important; // rounded-md
      }
       mat-icon {
        color: #9ca3af; // text-gray-400
      }
    }
    .filter-button {
      color: #374151; // text-gray-700
      border: 1px solid #d1d5db; // border-gray-300
      &:hover {
        background-color: #f9fafb; // hover:bg-gray-50
      }
    }
  }
}

.table-container {
  overflow-x: auto; // For responsiveness
  width: 100%;
}

.employee-table {
  width: 100%;
  min-width: 1200px; // Ensure horizontal scroll for many columns

  th.mat-header-cell {
    font-size: 0.75rem; // text-xs
    font-weight: 500; // font-medium
    color: #6b7280; // text-gray-500
    text-transform: uppercase;
    letter-spacing: 0.05em; // tracking-wider
    background-color: #f9fafb; // bg-gray-50
    padding: 12px 24px; // px-6 py-3
  }

  td.mat-cell {
    padding: 16px 24px; // px-6 py-4
    font-size: 0.875rem; // text-sm
    color: #374151; // text-gray-500 (default)
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: #e5e7eb; // divide-gray-200
  }
  td.mat-cell:first-of-type, th.mat-header-cell:first-of-type {
    padding-left: 24px;
  }
  td.mat-cell:last-of-type, th.mat-header-cell:last-of-type {
    padding-right: 24px;
  }


  .data-row:hover {
    background-color: #f9fafb; // hover:bg-gray-50
  }

  .chip-like {
    display: inline-flex;
    padding: 2px 8px; // px-2 py-1 (approx)
    font-size: 0.75rem; // text-xs
    font-weight: 600; // font-semibold
    border-radius: 9999px; // rounded-full
  }
  .bg-blue-100 { background-color: #dbeafe; }
  .text-blue-800 { color: #1e40af; }


  .utilization-cell {
    font-weight: 500; // font-medium
    margin-bottom: 4px; // mt-1 for progress bar
  }
  .utilization-bar {
    height: 6px !important; // h-1.5
    border-radius: 9999px; // rounded-full
    // Colors for progress bar track are handled by theme or direct style
    &.mat-progress-bar {
        background-color: #e5e7eb; // bg-gray-200 (track)
    }
  }
  // Utilization color classes
  .text-green-600 { color: #059669; }
  .text-blue-600 { color: #2563eb; }
  .text-yellow-600 { color: #ca8a04; }
  .text-red-600 { color: #dc2626; }

  .bg-green-500 ::ng-deep .mat-progress-bar-fill::after { background-color: #22c55e !important; }
  .bg-blue-500 ::ng-deep .mat-progress-bar-fill::after { background-color: #3b82f6 !important; }
  .bg-yellow-500 ::ng-deep .mat-progress-bar-fill::after { background-color: #eab308 !important; }
  .bg-red-500 ::ng-deep .mat-progress-bar-fill::after { background-color: #ef4444 !important; }
}

.mat-paginator {
  background-color: white;
  border-top: 1px solid #e5e7eb; // border-t border-gray-200
}

.mat-mdc-no-data-row .mat-mdc-cell { // For Angular 15+
    text-align: center;
    padding: 20px;
    color: #6b7280;
}
.mat-row .mat-no-data-row .mat-cell { // For older Angular Material
    text-align: center;
    padding: 20px;
    color: #6b7280;
}