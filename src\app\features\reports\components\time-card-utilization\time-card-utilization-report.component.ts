import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ChartComponent } from '../../../../shared/ui/chart/chart.component';
import { LoadingSpinnerComponent } from '../../../../shared/ui/loading-spinner/loading-spinner.component';

import { 
  employeesData, 
  utilizationByDepartmentData,
  billableHoursTrendData, 
  employeeTypeDistributionData,
  summaryCardsData,
  initialParameters,
  type UtilizationData,
  type BillableHoursData,
  type EmployeeTypeData,
  type SummaryCardData,
  type EmployeeData
} from '../../../../data/reports-data';

@Component({
  selector: 'app-time-card-utilization-report',
  templateUrl: './time-card-utilization-report.component.html',
  styleUrls: ['./time-card-utilization-report.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ChartComponent,
    LoadingSpinnerComponent
  ]
})
export class TimeCardUtilizationReportComponent implements OnInit {
  reportForm: FormGroup;
  isLoading = false;
  showFilters = false;
  
  // Data
  employees: EmployeeData[] = employeesData;
  utilizationByDepartment: UtilizationData[] = utilizationByDepartmentData;
  billableHoursTrend: BillableHoursData[] = billableHoursTrendData;
  employeeTypeDistribution: EmployeeTypeData[] = employeeTypeDistributionData;
  summaryCards: SummaryCardData = summaryCardsData;
  parameters = initialParameters;

  // Chart configurations
  departmentChartConfig = {
    type: 'bar' as const,
    data: {
      labels: this.utilizationByDepartment.map(d => d.name),
      datasets: [{
        data: this.utilizationByDepartment.map(d => d.value),
        backgroundColor: this.utilizationByDepartment.map(d => d.fill)
      }]
    },
    options: {
      indexAxis: 'y',
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        x: {
          beginAtZero: true,
          max: 100,
          title: {
            display: true,
            text: 'Utilization %'
          }
        }
      }
    }
  };

  hoursChartConfig = {
    type: 'line' as const,
    data: {
      labels: this.billableHoursTrend.map(d => d.month),
      datasets: [{
        data: this.billableHoursTrend.map(d => d.hours),
        borderColor: 'hsl(var(--primary))',
        backgroundColor: 'hsla(var(--primary), 0.1)',
        fill: true,
        tension: 0.4
      }]
    },
    options: {
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Hours'
          }
        }
      }
    }
  };

  typeChartConfig = {
    type: 'doughnut' as const,
    data: {
      labels: this.employeeTypeDistribution.map(d => d.type),
      datasets: [{
        data: this.employeeTypeDistribution.map(d => d.count),
        backgroundColor: this.employeeTypeDistribution.map(d => d.fill)
      }]
    },
    options: {
      plugins: {
        legend: {
          position: 'bottom'
        }
      }
    }
  };

  constructor(private fb: FormBuilder) {
    this.reportForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadReport();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      reportDate: [this.parameters.reportDate],
      supervisors: [this.parameters.supervisors],
      teamLeads: [this.parameters.teamLeads],
      departments: [this.parameters.departments],
      employmentStatus: [this.parameters.employmentStatus],
      layoutOptions: this.fb.group({
        showYTD: [this.parameters.layoutOptions.showYTD],
        showQuarterly: [this.parameters.layoutOptions.showQuarterly],
        showCurrent: [this.parameters.layoutOptions.showCurrent],
        showMonthly: [this.parameters.layoutOptions.showMonthly]
      })
    });
  }

  loadReport(): void {
    this.isLoading = true;
    // Simulate API call
    setTimeout(() => {
      this.isLoading = false;
    }, 1000);
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;
  }

  applyFilters(): void {
    this.loadReport();
    this.showFilters = false;
  }

  resetFilters(): void {
    this.reportForm.reset(this.parameters);
    this.loadReport();
  }

  scheduleReport(): void {
    // TODO: Implement schedule report dialog
  }

  exportReport(): void {
    // TODO: Implement export functionality
  }

  getTrendIcon(direction: 'up' | 'down'): string {
    return direction === 'up' ? 'trending_up' : 'trending_down';
  }

  getTrendClass(direction: 'up' | 'down', metric: string): string {
    // For non-billable hours, up is bad (red) and down is good (green)
    if (metric === 'nonBillable') {
      return direction === 'up' ? 'trend-down' : 'trend-up';
    }
    // For all other metrics, up is good (green) and down is bad (red)
    return direction === 'up' ? 'trend-up' : 'trend-down';
  }

  formatAmount(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  formatPercent(value: number): string {
    return `${value.toFixed(1)}%`;
  }

  formatHours(hours: number): string {
    return `${hours.toFixed(1)}h`;
  }
}