import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-employees',
  templateUrl: './employees.component.html',
  styleUrls: ['./employees.component.scss']
})
export class EmployeesComponent {
  @Input() employees: any[] = [];
  filteredEmployees: any[] = [];

  statuses = ['Active', 'On Leave', 'Terminated'];
  departments = ['Technology', 'Product', 'Design', 'Marketing', 'Analytics'];

  ngOnInit() {
    this.filteredEmployees = [...this.employees];
  }

  onSearch(query: string) {
    this.filteredEmployees = this.employees.filter(employee =>
      employee.name.toLowerCase().includes(query.toLowerCase()) ||
      employee.email.toLowerCase().includes(query.toLowerCase())
    );
  }

  onStatusChange(status: string) {
    this.filteredEmployees = status
      ? this.employees.filter(employee => employee.status === status)
      : [...this.employees];
  }

  onDepartmentChange(department: string) {
    this.filteredEmployees = department
      ? this.employees.filter(employee => employee.department === department)
      : [...this.employees];
  }

  onAddEmployee() {
    console.log('Add Employee button clicked');
  }
}
