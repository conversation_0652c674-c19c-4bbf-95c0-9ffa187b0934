.dashboard-container {
  width: 100%;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.dashboard-header h1 {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0;
  color: var(--color-text);
}

.actions {
  display: flex;
  gap: 0.75rem;
}

.primary-button, .secondary-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.primary-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
}

.primary-button:hover {
  background-color: var(--color-primary-light);
}

.secondary-button {
  background-color: transparent;
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.secondary-button:hover {
  background-color: hsla(var(--secondary), 0.8);
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background-color: var(--color-card);
  border-radius: var(--radius);
  padding: 1.5rem;
  display: flex;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.stat-icon .material-icons {
  font-size: 24px;
  color: white;
}

.people-icon {
  background-color: var(--color-primary);
}

.invoice-icon {
  background-color: #8B5CF6;
}

.time-icon {
  background-color: #14B8A6;
}

.attrition-icon {
  background-color: #F43F5E;
}

.stat-content {
  flex: 1;
}

.stat-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--muted-foreground);
  margin: 0 0 0.5rem 0;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  margin: 0;
}

.stat-trend .material-icons {
  font-size: 1rem;
  margin-right: 0.25rem;
}

.stat-trend.positive {
  color: #10B981;
}

.stat-trend.negative {
  color: #EF4444;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.dashboard-card {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.view-all {
  font-size: 0.875rem;
  color: var(--color-primary);
  text-decoration: none;
}

.view-all:hover {
  text-decoration: underline;
}

.card-content {
  padding: 1.5rem;
}

.activity-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.activity-item {
  display: flex;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--color-border);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 36px;
  height: 36px;
  border-radius: 9999px;
  background-color: hsla(var(--secondary), 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.activity-icon .material-icons {
  font-size: 1.25rem;
  color: var(--color-primary);
}

.activity-details {
  flex: 1;
}

.activity-description {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
}

.activity-time {
  margin: 0;
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.employees-table {
  width: 100%;
  border-collapse: collapse;
}

.employees-table th,
.employees-table td {
  padding: 0.75rem 1rem;
  text-align: left;
}

.employees-table th {
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--muted-foreground);
  border-bottom: 1px solid var(--color-border);
}

.employees-table tr:hover {
  background-color: hsla(var(--secondary), 0.5);
}

.employee-info {
  display: flex;
  align-items: center;
}

.employee-avatar {
  width: 32px;
  height: 32px;
  border-radius: 9999px;
  overflow: hidden;
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-primary);
  color: white;
  font-weight: 500;
}

.employee-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 2rem 0;
}

@media (max-width: 1200px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .actions {
    width: 100%;
  }
  
  .primary-button, .secondary-button {
    flex: 1;
    justify-content: center;
  }
  
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
}
