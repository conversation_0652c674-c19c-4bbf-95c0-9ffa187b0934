<header class="header">
  <div class="header-left">
    <div class="search-container" [class.expanded]="isSearchExpanded">
      <button class="search-button" (click)="toggleSearch()">
        <span class="material-icons">search</span>
      </button>
      <input 
        *ngIf="isSearchExpanded"
        id="search-input"
        type="text" 
        class="search-input fade-in" 
        placeholder="Search..." 
        [(ngModel)]="searchQuery" 
        (keyup.enter)="search()"
      />
    </div>
  </div>
  
  <div class="header-right">
    <div class="header-actions">
      <button class="action-button">
        <span class="material-icons">notifications</span>
      </button>
      <button class="action-button">
        <span class="material-icons">help_outline</span>
      </button>
    </div>
    
    <div class="user-profile">
      <div class="user-info">
        <div class="user-name">{{ user.name }}</div>
        <div class="user-role">{{ user.role }}</div>
      </div>
      <div class="user-avatar">
        <img [src]="user.avatar" alt="User Avatar" />
      </div>
    </div>
  </div>
</header>
