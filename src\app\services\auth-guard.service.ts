import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot } from '@angular/router';
import { Observable, map, switchMap, of } from 'rxjs';
import { AuthService } from './auth.service';
import { RoleService } from '../features/settings/data-access/role.service';


@Injectable({
  providedIn: 'root'
})
export class AuthGuardService implements CanActivate {
  constructor(
    private authService: AuthService,
    private roleService: RoleService,
    private router: Router
  ) {}

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean> {
    // // Temporarily allow all access to bypass auth checks
    // return of(true); 
    return this.authService.getCurrentUser().pipe(
      switchMap(user => {
        if (!user) {
          this.router.navigate(['/login']);
          return of(false);
        }

        const requiredPermission = route.data['requiredPermission'];
        if (!requiredPermission) {
          return of(true);
        }

        return this.roleService.getRoleById(user.roleId).pipe(
          map(role => {
            if (!role) {
              return false;
            }

            const hasPermission = role.permissions.includes(requiredPermission);
            if (!hasPermission) {
              this.router.navigate(['/unauthorized']);
              return false;
            }

            return true;
          })
        );
      })
    );
  }
}