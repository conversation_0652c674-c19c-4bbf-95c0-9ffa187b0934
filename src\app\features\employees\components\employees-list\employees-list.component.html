<div class="employees-container fade-in">
  <header class="employees-header">
    <div class="header-content">
      <h1>Employees</h1>
      <p class="subtitle">Manage and view your company's employees.</p>
    </div>
    <button class="add-employee-button" routerLink="/employees/add">
      <span class="material-icons">add</span>
      Add Employee
    </button>
  </header>
  
  <div class="filters-section">
    <div class="search-container">
      <span class="material-icons search-icon">search</span>
      <input
        type="text"
        class="search-input"
        placeholder="Search by name or email..."
        [(ngModel)]="searchQuery"
        (input)="onSearchChange()"
      />
    </div>
    
    <div class="filter-controls">
      <!-- Status Filter -->
      <div class="filter-dropdown" [class.active]="isStatusDropdownOpen">
        <button class="filter-button" (click)="toggleStatusDropdown()">
          <span>{{ selectedStatuses.length ? `${selectedStatuses.length} Selected` : 'All Statuses' }}</span>
          <span class="material-icons">{{ isStatusDropdownOpen ? 'expand_less' : 'expand_more' }}</span>
        </button>
        <div class="dropdown-menu" *ngIf="isStatusDropdownOpen">
          <div class="dropdown-item" *ngFor="let status of availableStatuses">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                [checked]="selectedStatuses.includes(status)"
                (change)="toggleStatus(status)"
              />
              <span>{{ status }}</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Department Filter -->
      <div class="filter-dropdown" [class.active]="isDepartmentDropdownOpen">
        <button class="filter-button" (click)="toggleDepartmentDropdown()">
          <span>{{ selectedDepartments.length ? `${selectedDepartments.length} Selected` : 'All Departments' }}</span>
          <span class="material-icons">{{ isDepartmentDropdownOpen ? 'expand_less' : 'expand_more' }}</span>
        </button>
        <div class="dropdown-menu" *ngIf="isDepartmentDropdownOpen">
          <div class="dropdown-item" *ngFor="let dept of departments">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                [checked]="selectedDepartments.includes(dept)"
                (change)="toggleDepartment(dept)"
              />
              <span>{{ dept }}</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <div class="view-toggle">
      <button class="view-button" [class.active]="view === 'cards'" (click)="setView('cards')">
        <span class="material-icons">grid_view</span>
        Cards
      </button>
      <button class="view-button" [class.active]="view === 'table'" (click)="setView('table')">
        <span class="material-icons">view_list</span>
        Table
      </button>
    </div>
  </div>

  <!-- Active Filters -->
  <div class="active-filters" *ngIf="hasActiveFilters()">
    <span class="filter-label">Active Filters:</span>
    <div class="filter-chips">
      <div class="filter-chip" *ngFor="let status of selectedStatuses">
        <span>Status: {{ status }}</span>
        <button class="remove-filter" (click)="removeStatus(status)">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="filter-chip" *ngFor="let dept of selectedDepartments">
        <span>Department: {{ dept }}</span>
        <button class="remove-filter" (click)="removeDepartment(dept)">
          <span class="material-icons">close</span>
        </button>
      </div>
      <button class="clear-filters" (click)="clearFilters()" *ngIf="hasActiveFilters()">
        Clear All
      </button>
    </div>
  </div>

  <div *ngIf="isLoading" class="loading-container">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <!-- Table View -->
  <div *ngIf="!isLoading && view === 'table'" class="employees-table-container">
    <table class="employees-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Title</th>
          <th>Email</th>
          <th>Location</th>
          <th>Department</th>
          <th>Status</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let employee of filteredEmployees">
          <td>
            <div class="employee-info">
              <div class="employee-avatar" *ngIf="employee.avatarUrl">
                <img [src]="employee.avatarUrl" [alt]="employee.firstName + ' ' + employee.lastName">
              </div>
              <div class="employee-avatar no-image" *ngIf="!employee.avatarUrl">
                {{ getInitials(employee.firstName, employee.lastName) }}
              </div>
              <span>{{ employee.firstName }} {{ employee.lastName }}</span>
            </div>
          </td>
          <td>{{ employee.jobTitle }}</td>
          <td>{{ employee.email }}</td>
          <td>{{ employee.location }}</td>
          <td>
            <span class="department-tag">{{ employee.department }}</span>
          </td>
          <td>
            <app-badge [type]="employee.status"></app-badge>
          </td>
          <td>
            <div class="actions">
              <button class="icon-button" [routerLink]="['/employees', employee.id]">
                <span class="material-icons">visibility</span>
              </button>
              <button class="icon-button" [routerLink]="['/employees', employee.id, 'edit']">
                <span class="material-icons">edit</span>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Cards View -->
  <div *ngIf="!isLoading && view === 'cards'" class="employees-grid">
    <div class="employee-card" *ngFor="let employee of filteredEmployees">
      <div class="card-header">
        <div class="employee-info">
          <div class="employee-avatar" *ngIf="employee.avatarUrl">
            <img [src]="employee.avatarUrl" [alt]="employee.firstName + ' ' + employee.lastName">
          </div>
          <div class="employee-avatar no-image" *ngIf="!employee.avatarUrl">
            {{ getInitials(employee.firstName, employee.lastName) }}
          </div>
          <div class="employee-details">
            <h3 class="employee-name">{{ employee.firstName }} {{ employee.lastName }}</h3>
            <p class="employee-title">{{ employee.jobTitle }}</p>
          </div>
        </div>
        <app-badge [type]="employee.status"></app-badge>
      </div>

      <div class="card-content">
        <div class="info-row">
          <span class="material-icons">email</span>
          <span>{{ employee.email }}</span>
        </div>
        <div class="info-row">
          <span class="material-icons">phone</span>
          <span>{{ employee.phoneNumber || 'N/A' }}</span>
        </div>
        <div class="info-row">
          <span class="material-icons">location_on</span>
          <span>{{ employee.location }} ({{ employee.locationType }})</span>
        </div>
      </div>

      <div class="card-footer">
        <div class="tags">
          <span class="department-tag">{{ employee.department }}</span>
          <span class="type-tag">{{ employee.employeeType }}</span>
        </div>
        <div class="actions">
          <button class="icon-button" [routerLink]="['/employees', employee.id]">
            <span class="material-icons">visibility</span>
          </button>
          <button class="icon-button" [routerLink]="['/employees', employee.id, 'edit']">
            <span class="material-icons">edit</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="!isLoading && filteredEmployees.length === 0" class="no-results">
    <div class="no-results-content">
      <span class="material-icons no-results-icon">search_off</span>
      <p>No employees found matching your search criteria.</p>
      <button class="clear-filters" (click)="clearFilters()">
        Clear Filters
      </button>
    </div>
  </div>
</div>