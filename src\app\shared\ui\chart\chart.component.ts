import { Component, Input, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import Chart from 'chart.js/auto';

@Component({
  selector: 'app-chart',
  template: `<canvas #chartCanvas></canvas>`,
  styles: []
})
export class ChartComponent implements AfterViewInit {
  @Input() type: 'bar' | 'line' | 'pie' | 'doughnut' = 'bar';
  @Input() data: any;
  @Input() options: any = {};
  @ViewChild('chartCanvas') chartCanvas!: ElementRef<HTMLCanvasElement>;
  
  private chart: Chart | null = null;
  
  ngAfterViewInit(): void {
    this.createChart();
  }
  
  ngOnChanges(): void {
    if (this.chart) {
      this.chart.destroy();
      this.createChart();
    }
  }
  
  private createChart(): void {
    if (!this.chartCanvas) return;
    
    const ctx = this.chartCanvas.nativeElement.getContext('2d');
    if (!ctx) return;
    
    const defaultOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            boxWidth: 12,
            padding: 15,
            font: {
              size: 12
            }
          }
        }
      }
    };
    
    this.chart = new Chart(ctx, {
      type: this.type,
      data: this.data,
      options: { ...defaultOptions, ...this.options }
    });
  }
}