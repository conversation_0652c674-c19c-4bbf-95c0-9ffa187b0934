import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { permissions, roles } from '../../auth/data-access/auth-data';
import { Permission, Role } from '../../auth/models/auth.model';


@Injectable({
  providedIn: 'root'
})
export class RoleService {
  constructor() {}

  getRoles(): Observable<Role[]> {
    return of(roles).pipe(delay(500));
  }

  getPermissions(): Observable<Permission[]> {
    return of(permissions).pipe(delay(500));
  }

  getRoleById(id: string): Observable<Role | undefined> {
    const role = roles.find(r => r.id === id);
    return of(role).pipe(delay(500));
  }

  addRole(role: Partial<Role>): Observable<Role> {
    const newRole: Role = {
      id: `role_${Date.now()}`,
      name: '',
      description: '',
      permissions: [],
      ...role
    };
    roles.push(newRole);
    return of(newRole).pipe(delay(800));
  }

  updateRole(id: string, updates: Partial<Role>): Observable<Role | undefined> {
    const index = roles.findIndex(r => r.id === id);
    if (index === -1) {
      return of(undefined);
    }
    
    const updatedRole = {
      ...roles[index],
      ...updates
    };
    roles[index] = updatedRole;
    return of(updatedRole).pipe(delay(800));
  }

  deleteRole(id: string): Observable<boolean> {
    const initialLength = roles.length;
    const index = roles.findIndex(r => r.id === id);
    if (index !== -1) {
      roles.splice(index, 1);
    }
    return of(roles.length < initialLength).pipe(delay(800));
  }

  addPermission(permission: Partial<Permission>): Observable<Permission> {
    const newPermission: Permission = {
      id: `perm_${Date.now()}`,
      name: '',
      description: '',
      category: '',
      ...permission
    };
    permissions.push(newPermission);
    return of(newPermission).pipe(delay(800));
  }

  updatePermission(id: string, updates: Partial<Permission>): Observable<Permission | undefined> {
    const index = permissions.findIndex(p => p.id === id);
    if (index === -1) {
      return of(undefined);
    }
    
    const updatedPermission = {
      ...permissions[index],
      ...updates
    };
    permissions[index] = updatedPermission;
    return of(updatedPermission).pipe(delay(800));
  }

  deletePermission(id: string): Observable<boolean> {
    const initialLength = permissions.length;
    const index = permissions.findIndex(p => p.id === id);
    if (index !== -1) {
      permissions.splice(index, 1);
    }
    return of(permissions.length < initialLength).pipe(delay(800));
  }
}