﻿<div class="distribution-container">
  <h3 class="component-title">Employee Type Distribution</h3>
  <div class="content-wrapper">
    <div class="chart-area">
      <!-- Placeholder for Pie Chart -->
      <app-chart-wrapper
        chartType="pie"
        [results]="chartData"
        [view]="[200, 200]"
        [legend]="false"
        [labels]="true"
        [doughnut]="true"
        [arcWidth]="0.35"
        [colorScheme]="pieChartColorScheme"
      ></app-chart-wrapper>
    </div>
    <div class="legend-area">
      <div *ngFor="let item of distributionData; let i = index" class="legend-item">
        <div class="legend-color-box" [style.backgroundColor]="getColorForType(i)"></div>
        <div class="legend-details">
          <div class="legend-label-count">
            <span class="legend-label">{{ item.type }}</span>
            <span class="legend-count">{{ item.count }} employees</span>
          </div>
          <div class="progress-bar-container">
            <div class="progress-bar" [style.width.%]="getPercentage(item.count)" [style.backgroundColor]="getColorForType(i)"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>