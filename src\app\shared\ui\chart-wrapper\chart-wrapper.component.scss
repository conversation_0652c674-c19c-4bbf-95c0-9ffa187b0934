﻿.chart-container {
  background-color: #f9fafb; // bg-gray-100 (mock chart background)
  padding: 16px; // p-4 (mock chart padding)
  border-radius: 0.375rem; // rounded
  height: 300px; // Approximate height from mock (h-64 + padding)
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid #e5e7eb; // border-gray-200
}

.chart-title {
  font-size: 1rem; // text-base
  font-weight: 500; // font-medium
  color: #374151; // text-gray-800
  margin-bottom: 16px; // mb-4
  align-self: flex-start;
}

.chart-placeholder {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #6b7280; // text-gray-500
  width: 100%;
}

.axis-labels-placeholder {
    width: 100%;
    font-size: 0.75rem; // text-xs
    color: #6b7280; // text-gray-500
    margin-top: 8px; // mb-2 (from mock structure)

    .x-axis-labels, .y-axis-labels {
        display: flex;
        justify-content: space-between;
        padding-top: 8px;
        border-top: 1px solid #e5e7eb; // h-px bg-gray-200
    }
     .y-axis-labels { // For vertical bar chart, Y axis labels are typically on the left
        flex-direction: column;
        align-items: flex-start;
        position: absolute; // This is a very rough mock
        left: 0;
        top: 50px; // Adjust
        height: calc(100% - 100px);
        justify-content: space-between;
        border-top: none;
        border-right: 1px solid #e5e7eb;
        padding-right: 4px;
        span {
            transform: rotate(-90deg); // This is not ideal, just for visual cue
        }
    }
}