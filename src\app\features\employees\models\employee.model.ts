export interface Employee {
  id: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  jobTitle: string;
  department: string;
  status: "Active" | "On Leave" | "Terminated";
  hireDate: string; // ISO date string
  avatarUrl?: string;
  salary?: number;
  performanceScore?: number; // 0-100
  engagementScore?: number; // 0-100
  yearsOfService?: number;
  lastReviewDate?: string; // ISO date string
  manager?: string;
  location?: string;
  locationType?: "Office" | "Remote" | "Hybrid";
  employeeType?: "Full-time" | "Part-time" | "Contractor";
  skills?: string[];
  notes?: string;
  dateOfBirth?: string;
}

export interface Department {
  id: string;
  name: string;
  description?: string;
  employeeCount?: number;
  color?: string;
}

export interface EmploymentType {
  id: string;
  name: string;
  description?: string;
  employeeCount?: number;
}

export interface EmployeeFormData {
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  jobTitle: string;
  department: string;
  hireDate: string;
  salary?: number;
  manager?: string;
  location?: string;
  locationType?: "Office" | "Remote" | "Hybrid";
  employeeType?: "Full-time" | "Part-time" | "Contractor";
  skills?: string[];
  notes?: string;
  dateOfBirth?: string;
}
