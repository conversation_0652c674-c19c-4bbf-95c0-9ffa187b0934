.settings-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.settings-header {
  margin-bottom: 2rem;
}

.settings-header h1 {
  font-size: 1.875rem;
  font-weight: 600;
  margin: 0;
}

.subtitle {
  color: var(--muted-foreground);
  margin: 0.5rem 0 0;
}

.settings-card {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.settings-form {
  padding: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-group input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px hsla(var(--primary), 0.1);
}

.form-actions {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
}

.primary-button,
.add-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.primary-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
}

.primary-button:hover {
  background-color: var(--color-primary-light);
}

.add-button {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.add-button:hover {
  background-color: hsla(var(--secondary), 0.8);
}

.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  text-align: left;
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--muted-foreground);
  border-bottom: 1px solid var(--color-border);
}

td {
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  border-bottom: 1px solid var(--color-border);
}

.department-name {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.color-preview {
  width: 24px;
  height: 24px;
  border-radius: var(--radius);
}

.actions {
  display: flex;
  gap: 0.5rem;
}

.icon-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  border-radius: 9999px;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.2s;
}

.icon-button:hover {
  background-color: hsla(var(--secondary), 0.8);
  color: var(--color-primary);
}

.department-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: hsla(var(--primary), 0.1);
  color: var(--color-primary);
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .settings-container {
    padding: 1rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-group.full-width {
    grid-column: auto;
  }

  .card-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .add-button {
    width: 100%;
    justify-content: center;
  }
}