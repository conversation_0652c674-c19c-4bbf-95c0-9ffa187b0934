<div class="report-container fade-in">
  <header class="report-header">
    <div class="header-content">
      <h1>Time Card Utilization Report</h1>
      <p class="subtitle">Track and analyze employee time utilization across departments</p>
    </div>
    <div class="header-actions">
      <button class="secondary-button" (click)="toggleFilters()">
        <span class="material-icons">filter_list</span>
        Filters
      </button>
      <button class="secondary-button" (click)="scheduleReport()">
        <span class="material-icons">schedule</span>
        Schedule
      </button>
      <button class="primary-button" (click)="exportReport()">
        <span class="material-icons">download</span>
        Export
      </button>
    </div>
  </header>

  <!-- Filters Panel -->
  <div class="filters-panel" *ngIf="showFilters">
    <form [formGroup]="reportForm" class="filters-form">
      <div class="filters-grid">
        <div class="form-group">
          <label for="reportDate">Report Date</label>
          <input type="date" id="reportDate" formControlName="reportDate">
        </div>

        <div class="form-group">
          <label for="supervisors">Supervisors</label>
          <select id="supervisors" formControlName="supervisors" multiple>
            <option *ngFor="let supervisor of parameters.supervisors" [value]="supervisor">
              {{supervisor}}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label for="teamLeads">Team Leads</label>
          <select id="teamLeads" formControlName="teamLeads" multiple>
            <option *ngFor="let lead of parameters.teamLeads" [value]="lead">
              {{lead}}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label for="departments">Departments</label>
          <select id="departments" formControlName="departments" multiple>
            <option *ngFor="let dept of parameters.departments" [value]="dept">
              {{dept}}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label for="employmentStatus">Employment Status</label>
          <select id="employmentStatus" formControlName="employmentStatus" multiple>
            <option *ngFor="let status of parameters.employmentStatus" [value]="status">
              {{status}}
            </option>
          </select>
        </div>
      </div>

      <div formGroupName="layoutOptions" class="layout-options">
        <label class="checkbox-label">
          <input type="checkbox" formControlName="showYTD">
          Show YTD
        </label>
        <label class="checkbox-label">
          <input type="checkbox" formControlName="showQuarterly">
          Show Quarterly
        </label>
        <label class="checkbox-label">
          <input type="checkbox" formControlName="showCurrent">
          Show Current
        </label>
        <label class="checkbox-label">
          <input type="checkbox" formControlName="showMonthly">
          Show Monthly
        </label>
      </div>

      <div class="filters-actions">
        <button type="button" class="secondary-button" (click)="resetFilters()">
          Reset
        </button>
        <button type="button" class="primary-button" (click)="applyFilters()">
          Apply Filters
        </button>
      </div>
    </form>
  </div>

  <div *ngIf="isLoading" class="loading-container">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div *ngIf="!isLoading" class="report-content">
    <!-- Summary Cards -->
    <div class="summary-cards">
      <div class="summary-card">
        <div class="card-content">
          <h3>Total Billable Hours</h3>
          <p class="card-value">{{ formatHours(summaryCards.billableHours.value) }}</p>
          <p class="card-trend" [class]="getTrendClass(summaryCards.billableHours.trendDirection, 'billable')">
            <span class="material-icons">{{ getTrendIcon(summaryCards.billableHours.trendDirection) }}</span>
            {{ formatPercent(summaryCards.billableHours.trend) }}
          </p>
        </div>
      </div>

      <div class="summary-card">
        <div class="card-content">
          <h3>Average Utilization</h3>
          <p class="card-value">{{ formatPercent(summaryCards.utilization.value) }}</p>
          <p class="card-trend" [class]="getTrendClass(summaryCards.utilization.trendDirection, 'utilization')">
            <span class="material-icons">{{ getTrendIcon(summaryCards.utilization.trendDirection) }}</span>
            {{ formatPercent(summaryCards.utilization.trend) }}
          </p>
        </div>
      </div>

      <div class="summary-card">
        <div class="card-content">
          <h3>Total Amount Billed</h3>
          <p class="card-value">{{ formatAmount(summaryCards.revenue.value) }}</p>
          <p class="card-trend" [class]="getTrendClass(summaryCards.revenue.trendDirection, 'amount')">
            <span class="material-icons">{{ getTrendIcon(summaryCards.revenue.trendDirection) }}</span>
            {{ formatPercent(summaryCards.revenue.trend) }}
          </p>
        </div>
      </div>

      <div class="summary-card">
        <div class="card-content">
          <h3>Non-Billable Hours</h3>
          <p class="card-value">{{ formatHours(summaryCards.nonBillableHours.value) }}</p>
          <p class="card-trend" [class]="getTrendClass(summaryCards.nonBillableHours.trendDirection, 'nonBillable')">
            <span class="material-icons">{{ getTrendIcon(summaryCards.nonBillableHours.trendDirection) }}</span>
            {{ formatPercent(summaryCards.nonBillableHours.trend) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Charts Grid -->
    <div class="charts-grid">
      <div class="chart-card">
        <div class="card-header">
          <h2>Utilization by Department</h2>
        </div>
        <div class="card-content">
          <app-chart [type]="departmentChartConfig.type" [data]="departmentChartConfig.data" [options]="departmentChartConfig.options"></app-chart>
        </div>
      </div>

      <div class="chart-card">
        <div class="card-header">
          <h2>Billable Hours Trend</h2>
        </div>
        <div class="card-content">
          <app-chart [type]="hoursChartConfig.type" [data]="hoursChartConfig.data" [options]="hoursChartConfig.options"></app-chart>
        </div>
      </div>

      <div class="chart-card">
        <div class="card-header">
          <h2>Employee Type Distribution</h2>
        </div>
        <div class="card-content">
          <app-chart [type]="typeChartConfig.type" [data]="typeChartConfig.data" [options]="typeChartConfig.options"></app-chart>
        </div>
      </div>
    </div>

    <!-- Employee Details Table -->
    <div class="employee-details-card">
      <div class="card-header">
        <h2>Employee Details</h2>
      </div>
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th>Employee</th>
              <th>Type</th>
              <th>Department</th>
              <th>Supervisor</th>
              <th>Team Lead</th>
              <th>Utilization</th>
              <th>Billable</th>
              <th>Non-Billable</th>
              <th>Total Hours</th>
              <th>Amount</th>
              <th>Comments</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let employee of employees">
              <td>{{ employee.firstName }} {{ employee.lastName }}</td>
              <td>{{ employee.employeeType }}</td>
              <td>{{ employee.department }}</td>
              <td>{{ employee.supervisor }}</td>
              <td>{{ employee.teamLead }}</td>
              <td>
                <span class="utilization-badge" [class.high]="employee.utilization >= 85" [class.medium]="employee.utilization >= 70 && employee.utilization < 85" [class.low]="employee.utilization < 70">
                  {{ formatPercent(employee.utilization) }}
                </span>
              </td>
              <td>{{ formatHours(employee.billable) }}</td>
              <td>{{ formatHours(employee.nonBillable) }}</td>
              <td>{{ formatHours(employee.totalHours) }}</td>
              <td>{{ formatAmount(employee.amount) }}</td>
              <td>
                <button *ngIf="employee.comments.length > 0" class="icon-button" [title]="employee.comments[0].text">
                  <span class="material-icons">comment</span>
                  {{ employee.comments.length }}
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>