﻿.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px; // px-6 py-4

  &.bg-blue-600 { background-color: #2563eb; } // From mock
  &.text-white { color: white; }

  h2.mat-dialog-title {
    margin: 0;
    font-size: 1.125rem; // text-lg
    font-weight: 500; // font-medium
  }
  button mat-icon {
    color: white; // Ensure icon is visible on dark bg
  }
}

.dialog-content {
  padding: 24px; // p-6
  max-height: 70vh; // Allow scrolling for long forms
  overflow-y: auto;

  .full-width {
    width: 100%;
    margin-bottom: 16px; // space-y-4 (applied as mb)
  }

  .form-group {
    margin-bottom: 16px;
    .group-label {
      display: block;
      font-size: 0.875rem; // text-sm
      font-weight: 500; // font-medium
      color: #374151; // text-gray-700
      margin-bottom: 8px; // mb-1 or mb-2
    }
    mat-checkbox, mat-radio-button {
      display: block; // For vertical stacking if needed, or inline-flex for side-by-side
      margin-bottom: 8px; // space-y-2
      font-size: 0.875rem;
      color: #374151;
    }
    .radio-group {
        display: flex;
        gap: 16px; // space-x-4
    }
  }
}

.dialog-actions {
  padding: 8px 24px 16px 24px; // pt-4 from mock, also default dialog padding
  border-top: 1px solid #e5e7eb; // border-t border-gray-200

  button mat-icon {
    margin-right: 8px;
  }
}