﻿<mat-card class="summary-card">
  <mat-card-content>
    <div class="card-header">
      <div>
        <p class="metric-title">{{ title }}</p>
        <p class="metric-value">{{ valueDisplay }}</p>
      </div>
      <div class="metric-icon-wrapper" [ngClass]="iconBgColorClass || 'bg-blue-100'">
        <mat-icon class="metric-icon" [ngClass]="iconColorClass || 'text-blue-600'">{{ iconName }}</mat-icon>
      </div>
    </div>
    <div *ngIf="trendPercentage !== undefined" class="trend-info">
      <mat-icon class="trend-icon" [ngClass]="trendDirection === 'up' ? 'text-green-500' : 'text-red-500'">
        {{ trendDirection === 'up' ? 'trending_up' : 'trending_down' }}
      </mat-icon>
      <span class="trend-percentage" [ngClass]="trendDirection === 'up' ? 'text-green-600' : 'text-red-600'">
        {{ trendPercentage | percent:'1.1-1' }}
      </span>
      <span class="trend-text">{{ trendText }}</span>
    </div>
  </mat-card-content>
</mat-card>