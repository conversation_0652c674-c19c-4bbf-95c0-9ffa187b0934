@use '@angular/material' as mat;

$primary: mat.define-palette(mat.$indigo-palette, 500, 300, 700);
$accent: mat.define-palette(mat.$pink-palette, 400, 200, 600);
$warn: mat.define-palette(mat.$red-palette);
$theme: mat.define-light-theme((
  color: (
    primary: $primary,
    accent: $accent,
    warn: $warn
  ),
  typography: mat.define-typography-config(),
  density: 0
));

@include mat.core();
@include mat.all-component-themes($theme);

// Optional: For gradient pills and subtle animations
:root {
  --gradient-primary: linear-gradient(135deg, #5c6bc0, #ab47bc);
  --gradient-accent: linear-gradient(135deg, #ff4081, #f50057);
  --pill-radius: 999px;
  --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
