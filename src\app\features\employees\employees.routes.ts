import { Routes } from '@angular/router';
import { AuthGuard } from '@core/guards/auth.guard';


export const employeesRoutes: Routes = [
  {
    path: '',
    loadComponent: () => import('./components/employees-list/employees-list.component').then(m => m.EmployeesListComponent),
    canActivate: [AuthGuard],
    data: { requiredPermission: 'perm_user_read' }
  },
  {
    path: 'add',
    loadComponent: () => import('./components/add-employee/add-employee.component').then(m => m.AddEmployeeComponent),
    canActivate: [AuthGuard],
    data: { requiredPermission: 'perm_user_manage' }
  },
  {
    path: ':id',
    loadComponent: () => import('./components/employee-detail/employee-detail.component').then(m => m.EmployeeDetailComponent),
    canActivate: [AuthGuard],
    data: { requiredPermission: 'perm_user_read' }
  },
  {
    path: ':id/edit',
    loadComponent: () => import('./components/employee-detail/employee-detail.component').then(m => m.EmployeeDetailComponent),
    canActivate: [AuthGuard],
    data: { requiredPermission: 'perm_user_manage' }
  }
];
