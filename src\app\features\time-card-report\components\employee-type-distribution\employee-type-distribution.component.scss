﻿.distribution-container {
  padding: 24px; // p-6 from mock card
}

.component-title {
  font-size: 1rem; // text-base
  font-weight: 500; // font-medium
  color: #374151; // text-gray-800
  margin-bottom: 16px; // mb-4
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;

  @media (min-width: 768px) { // md:flex-row
    flex-direction: row;
    align-items: flex-start; // Align items to start for row layout
  }
}

.chart-area {
  width: 100%; // Full width on small screens
  max-width: 250px; // Limit pie chart size
  margin-bottom: 24px;

  @media (min-width: 768px) {
    width: 33.3333%; // w-1/3
    margin-bottom: 0;
    margin-right: 24px; // pl-6 (applied as margin to separate)
    display: flex;
    justify-content: center;
    align-items: center;
  }

  app-chart-wrapper { // Ensure chart wrapper fits
    width: 100%;
    height: auto; // Let chart define its height
  }
}

.legend-area {
  width: 100%;
  @media (min-width: 768px) {
    width: 66.6666%; // w-2/3
  }
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px; // space-y-4 (applied to each item)

  &:last-child {
    margin-bottom: 0;
  }
}

.legend-color-box {
  width: 16px; // w-4
  height: 16px; // h-4
  border-radius: 50%; // rounded-full
  margin-right: 8px; // mr-2
  flex-shrink: 0;
}

.legend-details {
  flex-grow: 1;
}

.legend-label-count {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px; // mb-1
}

.legend-label {
  font-size: 0.875rem; // text-sm
  font-weight: 500; // font-medium
  color: #374151; // text-gray-700
}

.legend-count {
  font-size: 0.875rem; // text-sm
  color: #6b7280; // text-gray-500
}

.progress-bar-container {
  width: 100%;
  background-color: #e5e7eb; // bg-gray-200
  border-radius: 9999px; // rounded-full
  height: 8px; // h-2
}

.progress-bar {
  height: 8px; // h-2
  border-radius: 9999px; // rounded-full
}