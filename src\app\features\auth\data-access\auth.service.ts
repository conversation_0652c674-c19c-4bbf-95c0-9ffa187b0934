import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { delay } from 'rxjs/operators';
import { User } from '../../../shared/models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private user: User | null = {
    id: 'user-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    roleId: 'admin-role',
    status: 'active',
    avatarUrl: 'https://placehold.co/100x100.png'
  };

  constructor() {}

  login(email: string, password: string): Observable<User> {
    // Simulate authentication logic
    if (email === '<EMAIL>') {
      return throwError(() => new Error('Invalid credentials'));
    }

    const mockUser: User = {
      id: 'user-1',
      name: '<PERSON>',
      email: email,
      roleId: 'role_admin',
      status: 'active',
      avatarUrl: 'https://placehold.co/100x100.png'
    };

    this.user = mockUser;
    return of(mockUser).pipe(delay(800));
  }

  logout(): Observable<boolean> {
    this.user = null;
    return of(true).pipe(delay(500));
  }

  getCurrentUser(): Observable<User | null> {
    return of(this.user).pipe(delay(300));
  }

  isAuthenticated(): Observable<boolean> {
    return of(this.user !== null).pipe(delay(300));
  }
}
