﻿<div class="page-container">
  <header class="page-header">
    <h1 class="page-title">Employee Time Card Utilization Report</h1>
    <mat-button-toggle-group [(ngModel)]="viewMode" aria-label="View Mode">
      <mat-button-toggle value="dashboard" (click)="setViewMode('dashboard')">
        <mat-icon>bar_chart</mat-icon> Dashboard
      </mat-button-toggle>
      <mat-button-toggle value="table" (click)="setViewMode('table')">
        <mat-icon>list</mat-icon> Data Table
      </mat-button-toggle>
    </mat-button-toggle-group>
  </header>

  <app-report-parameters
    [initialParameters]="reportParameters"
    (runReport)="onRunReport($event)"
    (scheduleReport)="onScheduleReport()"
    (exportToExcel)="onExportToExcel()"
    (parametersChanged)="onParametersChanged($event)"
  ></app-report-parameters>

  <app-dashboard-view
    *ngIf="viewMode === 'dashboard'"
    [summaryMetrics]="dashboardSummaryMetrics"
    [utilizationByDeptData]="utilizationByDeptChartData"
    [billableHoursTrendData]="billableHoursTrendChartData"
    [employeeTypeDistributionData]="employeeTypeDistributionData"
  ></app-dashboard-view>

  <app-data-table-view
    *ngIf="viewMode === 'table' || viewMode === 'dashboard'"
    [class.partially-hidden]="viewMode === 'dashboard'"
    [employeeData]="employeeTableData"
    (searchChanged)="onSearchChanged($event)"
  ></app-data-table-view>
</div>