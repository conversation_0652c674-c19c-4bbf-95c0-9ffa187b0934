.settings-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.settings-header {
  margin-bottom: 2rem;
}

.settings-header h1 {
  font-size: 1.875rem;
  font-weight: 600;
  margin: 0;
}

.subtitle {
  color: var(--muted-foreground);
  margin: 0.5rem 0 0;
}

.settings-card {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--muted-foreground);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--destructive);
}

.connection-status.connected .status-dot {
  background-color: #10B981;
}

.settings-form {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.required {
  color: var(--destructive);
}

.input-container input,
.input-container select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: all 0.2s;
}

.input-container input:focus,
.input-container select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px hsla(var(--primary), 0.1);
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.primary-button,
.secondary-button,
.destructive-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.primary-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
}

.primary-button:hover:not(:disabled) {
  background-color: var(--color-primary-light);
}

.secondary-button {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.secondary-button:hover:not(:disabled) {
  background-color: hsla(var(--secondary), 0.8);
}

.destructive-button {
  background-color: var(--destructive);
  color: white;
  border: none;
}

.destructive-button:hover:not(:disabled) {
  opacity: 0.9;
}

button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.settings-content {
  padding: 1.5rem;
}

.sync-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
}

.sync-schedule {
  background-color: hsla(var(--secondary), 0.5);
  padding: 1rem;
  border-radius: var(--radius);
}

.sync-schedule h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
}

.sync-schedule p {
  margin: 0 0 1rem;
  font-size: 0.875rem;
}

@media (max-width: 640px) {
  .settings-container {
    padding: 1rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .primary-button,
  .secondary-button,
  .destructive-button {
    width: 100%;
    justify-content: center;
  }
}