import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { Employee } from '../../../../models/employee.model';
import { LoadingSpinnerComponent } from '../../../../shared/ui/loading-spinner/loading-spinner.component';
import { BadgeComponent } from '../../../../shared/ui/badge/badge.component';
import { EmployeeService } from '../../data-access/employee.service';


@Component({
  selector: 'app-employees-list',
  templateUrl: './employees-list.component.html',
  styleUrls: ['./employees-list.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule, LoadingSpinnerComponent, BadgeComponent]
})
export class EmployeesListComponent implements OnInit {
  employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
  isLoading = true;
  searchQuery = '';
  view: string = 'cards';
  
  // Filter states
  isStatusDropdownOpen = false;
  isDepartmentDropdownOpen = false;
  selectedStatuses: string[] = [];
  selectedDepartments: string[] = [];
  
  availableStatuses = ['Active', 'On Leave', 'Terminated'];
  departments = ['Technology', 'Product', 'Design', 'Marketing', 'Analytics', 'HR', 'Finance', 'IT'];
  
  constructor(private employeeService: EmployeeService) {}
  
  ngOnInit(): void {
    this.loadEmployees();
  }
  
  loadEmployees(): void {
    this.employeeService.getEmployees().subscribe(
      (employees) => {
        this.employees = employees;
        this.applyFilters();
        this.isLoading = false;
      },
      (error) => {
        console.error('Error loading employees:', error);
        this.isLoading = false;
      }
    );
  }
  
  toggleStatusDropdown(): void {
    this.isStatusDropdownOpen = !this.isStatusDropdownOpen;
    this.isDepartmentDropdownOpen = false;
  }
  
  toggleDepartmentDropdown(): void {
    this.isDepartmentDropdownOpen = !this.isDepartmentDropdownOpen;
    this.isStatusDropdownOpen = false;
  }
  
  toggleStatus(status: string): void {
    const index = this.selectedStatuses.indexOf(status);
    if (index === -1) {
      this.selectedStatuses.push(status);
    } else {
      this.selectedStatuses.splice(index, 1);
    }
    this.applyFilters();
  }
  
  toggleDepartment(department: string): void {
    const index = this.selectedDepartments.indexOf(department);
    if (index === -1) {
      this.selectedDepartments.push(department);
    } else {
      this.selectedDepartments.splice(index, 1);
    }
    this.applyFilters();
  }
  
  removeStatus(status: string): void {
    const index = this.selectedStatuses.indexOf(status);
    if (index !== -1) {
      this.selectedStatuses.splice(index, 1);
      this.applyFilters();
    }
  }
  
  removeDepartment(department: string): void {
    const index = this.selectedDepartments.indexOf(department);
    if (index !== -1) {
      this.selectedDepartments.splice(index, 1);
      this.applyFilters();
    }
  }
  
  hasActiveFilters(): boolean {
    return this.selectedStatuses.length > 0 || this.selectedDepartments.length > 0;
  }
  
  clearFilters(): void {
    this.selectedStatuses = [];
    this.selectedDepartments = [];
    this.searchQuery = '';
    this.applyFilters();
  }
  
  onSearchChange(): void {
    this.applyFilters();
  }
  
  applyFilters(): void {
    let result = this.employees;
    
    // Apply status filters
    if (this.selectedStatuses.length > 0) {
      result = result.filter(employee => this.selectedStatuses.includes(employee.status));
    }
    
    // Apply department filters
    if (this.selectedDepartments.length > 0) {
      result = result.filter(employee => this.selectedDepartments.includes(employee.department));
    }
    
    // Apply search filter
    if (this.searchQuery.trim() !== '') {
      const query = this.searchQuery.toLowerCase();
      result = result.filter(employee => 
        employee.firstName.toLowerCase().includes(query) ||
        employee.lastName.toLowerCase().includes(query) ||
        employee.email.toLowerCase().includes(query) ||
        employee.jobTitle.toLowerCase().includes(query) ||
        employee.department.toLowerCase().includes(query)
      );
    }
    
    this.filteredEmployees = result;
  }
  
  getInitials(firstName: string, lastName: string): string {
    return firstName.charAt(0) + lastName.charAt(0);
  }

  setView(view: string): void {
    this.view = view;
  }
}