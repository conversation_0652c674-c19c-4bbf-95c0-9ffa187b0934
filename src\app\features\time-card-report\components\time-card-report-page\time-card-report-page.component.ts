﻿import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms'; // For ngModel
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatDialog } from '@angular/material/dialog';

import { ReportParametersComponent } from '../report-parameters/report-parameters.component';
import { DashboardViewComponent } from '../dashboard-view/dashboard-view.component';
import { DataTableViewComponent } from '../data-table-view/data-table-view.component';
import { ScheduleReportDialogComponent } from '../schedule-report-dialog/schedule-report-dialog.component';

import { ReportParameters, initialReportParameters } from '../../models/report-parameters.model';
import { EmployeeTimeCard } from '../../models/employee.model';
import { SummaryMetric, ChartDataPoint, EmployeeTypeDistribution, MonthlyTrendDataPoint } from '../../models/dashboard-data.model';
import { ScheduleReportConfig } from '../../models/schedule-report.model';

@Component({
  selector: 'app-time-card-report-page',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonToggleModule,
    MatIconModule,
    ReportParametersComponent,
    DashboardViewComponent,
    DataTableViewComponent,
  ],
  templateUrl: './time-card-report-page.component.html',
  styleUrls: ['./time-card-report-page.component.scss'],
})
export class TimeCardReportPageComponent implements OnInit {
  viewMode: 'dashboard' | 'table' = 'dashboard';
  reportParameters: ReportParameters = JSON.parse(JSON.stringify(initialReportParameters)); // Deep copy

  // Placeholder data - replace with actual data from services
  dashboardSummaryMetrics: SummaryMetric[] = [];
  utilizationByDeptChartData: ChartDataPoint[] = [];
  billableHoursTrendChartData: MonthlyTrendDataPoint[] = [];
  employeeTypeDistributionData: EmployeeTypeDistribution[] = [];
  employeeTableData: EmployeeTimeCard[] = [];

  constructor(public dialog: MatDialog) {}

  ngOnInit(): void {
    this.loadInitialData();
  }

  loadInitialData(): void {
    // Simulate fetching data
    this.dashboardSummaryMetrics = [
      { title: 'Total Billable Hours', value: 146.5, iconName: 'schedule', trendPercentage: 0.125, trendDirection: 'up', iconBgColorClass: 'bg-blue-100', iconColorClass: 'text-blue-600' },
      { title: 'Average Utilization', value: 84.2, valueType: 'percent', iconName: 'insert_chart_outlined', trendPercentage: 0.032, trendDirection: 'up', iconBgColorClass: 'bg-green-100', iconColorClass: 'text-green-600' },
      { title: 'Total Amount Billed', value: 23075, valueType: 'currency', iconName: 'attach_money', trendPercentage: 0.087, trendDirection: 'up', iconBgColorClass: 'bg-green-100', iconColorClass: 'text-green-600' },
      { title: 'Non-Billable Hours', value: 35.5, iconName: 'warning_amber', trendPercentage: 0.042, trendDirection: 'down', iconBgColorClass: 'bg-yellow-100', iconColorClass: 'text-yellow-600', trendText: 'from last month (increase)' },
    ];

    this.utilizationByDeptChartData = [
      { name: 'Engineering', value: 84.2 }, { name: 'Marketing', value: 92.0 },
      { name: 'Human Resources', value: 76.3 }, { name: 'Finance', value: 88.5 },
      { name: 'Product', value: 79.8 }
    ];
    this.billableHoursTrendChartData = [
      { name: 'Sep', value: 1250 }, { name: 'Oct', value: 1300 }, { name: 'Nov', value: 1180 },
      { name: 'Dec', value: 980 }, { name: 'Jan', value: 1420 }, { name: 'Feb', value: 1560 }
    ];
    this.employeeTypeDistributionData = [
      { type: 'Full-time', count: 28, colorClass: 'bg-blue-500' },
      { type: 'Part-time', count: 7, colorClass: 'bg-green-500' },
      { type: 'Contractor', count: 12, colorClass: 'bg-yellow-500' }
    ];
    this.employeeTableData = [
        { id: 'EMP001', firstName: 'John', lastName: 'Smith', employeeType: 'Full-time', department: 'Engineering', supervisor: 'Jane Doe', utilization: 87.5, billable: 35.0, nonBillable: 5.0, totalHours: 40.0, amount: 5250.00 },
        { id: 'EMP002', firstName: 'Sarah', lastName: 'Johnson', employeeType: 'Part-time', department: 'Marketing', supervisor: 'Bob Williams', utilization: 92.0, billable: 20.0, nonBillable: 2.0, totalHours: 22.0, amount: 3300.00 },
        { id: 'EMP003', firstName: 'David', lastName: 'Rodriguez', employeeType: 'Contractor', department: 'Engineering', supervisor: 'Jane Doe', utilization: 100.0, billable: 40.0, nonBillable: 0.0, totalHours: 40.0, amount: 6800.00 },
        { id: 'EMP004', firstName: 'Emily', lastName: 'Chen', employeeType: 'Full-time', department: 'Human Resources', supervisor: 'Michael Brown', utilization: 76.3, billable: 25.5, nonBillable: 14.5, totalHours: 40.0, amount: 3825.00 },
        { id: 'EMP005', firstName: 'James', lastName: 'Williams', employeeType: 'Full-time', department: 'Engineering', supervisor: 'Jane Doe', utilization: 65.0, billable: 26.0, nonBillable: 14.0, totalHours: 40.0, amount: 3900.00 }
    ];
  }

  setViewMode(mode: 'dashboard' | 'table'): void {
    this.viewMode = mode;
  }

  onRunReport(params: ReportParameters): void {
    console.log('Running report with parameters:', params);
    // TODO: Call data-access service to fetch new report data
    alert('Running report... (check console for parameters)');
  }

  onScheduleReport(): void {
    const dialogRef = this.dialog.open(ScheduleReportDialogComponent, {
      width: '500px', // max-w-md
      data: { currentParameters: this.reportParameters } // Pass current params if needed by dialog
    });

    dialogRef.afterClosed().subscribe((result: ScheduleReportConfig | undefined) => {
      if (result) {
        console.log('Schedule saved:', result);
        // TODO: Call service to save schedule
        alert('Report schedule saved! (Check console for details)');
      }
    });
  }

  onExportToExcel(): void {
    console.log('Exporting to Excel with current parameters:', this.reportParameters);
    // TODO: Implement export functionality
    alert('Exporting report to Excel... (simulated)');
  }

  onParametersChanged(params: ReportParameters): void {
    this.reportParameters = params;
    console.log('Report parameters updated in parent:', params);
  }

  onSearchChanged(searchTerm: string): void {
    console.log('Search term from table view:', searchTerm);
    // TODO: Filter employeeTableData or re-fetch data
  }
}