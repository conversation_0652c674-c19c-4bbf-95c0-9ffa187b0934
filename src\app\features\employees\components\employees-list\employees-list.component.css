.employees-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.employees-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.header-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  margin: 0;
  color: var(--color-text);
}

.subtitle {
  margin: 0.5rem 0 0;
  color: var(--muted-foreground);
  font-size: 0.875rem;
}

.add-employee-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-employee-button:hover {
  background-color: var(--color-primary-light);
}

.filters-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--muted-foreground);
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.75rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  background-color: var(--color-card);
  font-size: 0.875rem;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px hsla(var(--primary), 0.1);
}

.filter-controls {
  display: flex;
  gap: 0.75rem;
}

.filter-dropdown {
  position: relative;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s;
}

.filter-button:hover {
  background-color: hsla(var(--secondary), 0.8);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 0.5rem;
  min-width: 200px;
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.dropdown-item {
  padding: 0.5rem 1rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.active-filters {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.filter-label {
  font-size: 0.875rem;
  color: var(--muted-foreground);
}

.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-chip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background-color: hsla(var(--primary), 0.1);
  border-radius: 9999px;
  font-size: 0.75rem;
  color: var(--color-primary);
}

.remove-filter {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
}

.remove-filter .material-icons {
  font-size: 1rem;
}

.view-toggle {
  display: flex;
  gap: 0.5rem;
}

.view-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: none;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s;
}

.view-button:hover {
  background-color: hsla(var(--secondary), 0.8);
}

.view-button.active {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.employees-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.employee-card {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.employee-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.employee-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.employee-avatar {
  width: 48px;
  height: 48px;
  border-radius: 9999px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-primary);
  color: white;
  font-weight: 500;
  font-size: 1rem;
}

.employee-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.employee-details {
  display: flex;
  flex-direction: column;
}

.employee-name {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.employee-title {
  margin: 0.25rem 0 0;
  font-size: 0.875rem;
  color: var(--muted-foreground);
}

.card-content {
  padding: 1.25rem;
  border-bottom: 1px solid var(--color-border);
}

.info-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: var(--color-text);
  margin-bottom: 0.75rem;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row .material-icons {
  font-size: 1.25rem;
  color: var(--muted-foreground);
}

.card-footer {
  padding: 1rem 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tags {
  display: flex;
  gap: 0.5rem;
}

.department-tag,
.type-tag {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.department-tag {
  background-color: hsla(var(--primary), 0.1);
  color: var(--color-primary);
}

.type-tag {
  background-color: hsla(var(--secondary), 0.8);
  color: var(--secondary-foreground);
}

.actions {
  display: flex;
  gap: 0.5rem;
}

.icon-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  border-radius: 9999px;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.2s;
}

.icon-button:hover {
  background-color: hsla(var(--secondary), 0.8);
  color: var(--color-primary);
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 3rem 0;
}

.no-results {
  text-align: center;
  padding: 3rem 0;
}

.no-results-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.no-results-icon {
  font-size: 3rem;
  color: var(--muted-foreground);
}

.clear-filters {
  padding: 0.5rem 1rem;
  background: none;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  color: var(--color-text);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.clear-filters:hover {
  background-color: hsla(var(--secondary), 0.8);
}

@media (max-width: 768px) {
  .employees-container {
    padding: 0 1rem;
  }

  .employees-header {
    flex-direction: column;
    gap: 1rem;
  }

  .add-employee-button {
    width: 100%;
    justify-content: center;
  }

  .filters-section {
    flex-direction: column;
  }

  .search-container {
    max-width: none;
  }

  .filter-controls {
    width: 100%;
  }

  .filter-dropdown {
    flex: 1;
  }

  .filter-button {
    width: 100%;
    justify-content: space-between;
  }

  .employees-grid {
    grid-template-columns: 1fr;
  }
}