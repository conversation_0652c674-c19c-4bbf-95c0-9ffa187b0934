<div class="sidebar">
  <div class="sidebar-header">
    <h2 class="app-title">Pathfinder Analytics</h2>
  </div>
  
  <nav class="sidebar-nav">
    <ul>
      <li *ngFor="let link of navLinks">
        <!-- Main nav link -->
        <a [routerLink]="link.href"
           [routerLinkActive]="'active'"
           [routerLinkActiveOptions]="{ exact: !link.subLinks }"
           (click)="$event.preventDefault(); toggleSubMenu(link.href)"
           class="sidebar-link">
          <span class="material-icons nav-icon">{{ link.icon }}</span>
          <span class="nav-label">{{ link.label }}</span>
          <span *ngIf="link.subLinks" class="material-icons expand-icon">
            {{ expandedItems[link.href] ? 'expand_less' : 'expand_more' }}
          </span>
        </a>
        
        <!-- Sub nav links -->
        <ul *ngIf="link.subLinks && expandedItems[link.href]" class="sub-nav slide-in">
          <li *ngFor="let subLink of link.subLinks">
            <a [routerLink]="subLink.href" 
               [routerLinkActive]="'active'"
               [class.disabled]="subLink.disabled"
               class="sidebar-sublink">
              <span class="material-icons sub-nav-icon">{{ subLink.icon }}</span>
              <span class="sub-nav-label">{{ subLink.label }}</span>
            </a>
          </li>
        </ul>
      </li>
    </ul>
  </nav>
  
  <div class="sidebar-footer">
    <ul>
      <li *ngFor="let link of profileNavLinks">
        <a [routerLink]="link.href" 
           [routerLinkActive]="'active'"
           class="sidebar-link">
          <span class="material-icons nav-icon">{{ link.icon }}</span>
          <span class="nav-label">{{ link.label }}</span>
        </a>
      </li>
    </ul>
  </div>
</div>
